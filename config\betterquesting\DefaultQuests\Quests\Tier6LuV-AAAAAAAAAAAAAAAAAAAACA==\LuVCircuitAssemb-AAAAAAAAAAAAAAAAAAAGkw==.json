{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1490}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "You may have noticed that the circuits you're thinking of crafting cost way more, 6x more. This is intended for circuits needing LuV+ voltage. You now need to craft your circuits in the Circuit Assembly Line or CAL if you don't want to pay out the ears in materials. You'll still need to craft your first ones using the normal Circuit Assembler though.\n\nOnce you've done that, you can make an imprint for use in the CAL to set it to make that (and only that) type of circuit.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 1185, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§c§lLuV Circuit Assembler", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1683, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 4, "Damage:2": 88, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedingot"}, "1:10": {"Count:3": 8, "Damage:2": 32103, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}, "2:10": {"Count:3": 1, "Damage:2": 41, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianIII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 1185, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}}}