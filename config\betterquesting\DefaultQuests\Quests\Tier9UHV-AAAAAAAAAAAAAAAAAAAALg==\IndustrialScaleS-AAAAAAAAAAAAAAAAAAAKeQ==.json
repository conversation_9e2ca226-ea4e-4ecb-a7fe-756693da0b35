{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2657}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "So you probably noticed that the amount of seaweed you'll need to make Bio circuits is kind of...intense. Collecting them yourself is a huge pain, and who wants to program robots? Plus servers. Fortunately, there's now a way to make seaweed.\n\nThe first thing you need is...Seaweed! Use that with Unknown Water you pumped from the same planet to make the culture. Expect this to take a while, as the chances are super low. You'll also need the max tier Bio Lab.\n\nOnce you have the culture you can make SeaweedBroth, by combining it with UnknownNutrientAgar and some other stuff. This can then be made into seaweed.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "GalaxySpace:tcetiedandelions"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Industrial Scale Seaweed Production", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2681, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 12704, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Breedable:1": 1, "Color:11": [27, 153, 94], "DNA:10": {"Chance:3": 750, "Name:8": "TCetiEis <PERSON>", "Rarity:1": 3, "Tier:3": 2}, "Fluid:8": "tcetieisfucusserratusfluid", "Name:8": "TCetiEis <PERSON>", "Plasmid:10": {"Chance:3": 750, "Name:8": "TCetiEis <PERSON>", "Rarity:1": 3, "Tier:3": 2}, "Rarety:1": 3}}, "1:10": {"Count:3": 1, "Damage:2": 7, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.98"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 8, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.98"}}, "taskID:8": "bq_standard:optional_retrieval"}, "3:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 3, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 64, "Damage:2": 0, "OreDict:8": "", "id:8": "GalaxySpace:tcetiedandelions"}}, "taskID:8": "bq_standard:retrieval"}}}