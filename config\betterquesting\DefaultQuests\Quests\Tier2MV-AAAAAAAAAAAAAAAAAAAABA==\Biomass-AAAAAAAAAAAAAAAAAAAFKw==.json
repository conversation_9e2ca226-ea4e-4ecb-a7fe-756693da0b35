{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 734}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 3070}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 735}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Chose the IC2 or the gregtech way to make biomass. Water and Bio Chaff is needed.\n\nYou can also use the pyrolyze oven to make biomass in a more efficient way if you make it.\n\nNote that forestry has its own biomass as well. It can also be made into fermented biomass like this kind. Choose which kind you want to make later on yourself.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 6, "OreDict:8": "", "id:8": "IC2:itemCellEmpty"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§5§6§lBiomass", "partySingleReward:1": 0, "questLogic:8": "OR", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "OR", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1323, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 2, "Damage:2": 1366, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "1:10": {"Count:3": 1, "Damage:2": 32702, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 32, "Damage:2": 0, "OreDict:8": "", "id:8": "IC2:itemCellEmpty"}, "3:10": {"Count:3": 1, "Damage:2": 5, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinChemist"}, "1:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 64, "Damage:2": 6, "OreDict:8": "", "id:8": "IC2:itemCellEmpty"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 64, "Damage:2": 30704, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}}}