{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2627}, "1:10": {"questIDHigh:4": -5755629537772091783, "questIDLow:4": -7406677140276529890}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 2631}, "3:10": {"questIDHigh:4": 0, "questIDLow:4": 1213}, "4:10": {"questIDHigh:4": 0, "questIDLow:4": 2622}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Now that you have some Europium, put it to use by making a MK II reactor.\n\nThis one will require the same block placement, but replaces LuV casings with fusion machine casings, superconducting coil blocks with fusion coil blocks, and uses ZPM tier hatches.\n\nEach of the up to 16 ZPM Energy Hatches adds 8192 EU/t power input and 20 MEU starting capacity. Therefore this reactor can handle recipes up to 320M EU at startup. In addition, if you perform a MK I recipe on a MK II, you will overclock it. Fusion overclocking is different from normal GT. It will take 2x the power and the recipe will go 2x faster.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 1194, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Fusion Reactor MKII - <PERSON><PERSON>, Better, Fusor", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1197, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 4, "Damage:2": 32091, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}, "1:10": {"Count:3": 4, "Damage:2": 32607, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 2, "Damage:2": 32616, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "3:10": {"Count:3": 2, "Damage:2": 32636, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "4:10": {"Count:3": 2, "Damage:2": 32646, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "5:10": {"Count:3": 64, "Damage:2": 2420, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianIII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 47, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "1:10": {"Count:3": 1, "Damage:2": 67, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "2:10": {"Count:3": 2, "Damage:2": 57, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 32, "Damage:2": 6, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings4"}, "1:10": {"Count:3": 32, "Damage:2": 7, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings4"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 1194, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}}}