{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2649}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Ok, so let's say you've made some kind of culture, but you want more of it? You only need one per Vat, but they're consumed when making DNA Samples, so you should probably learn how.\n\nTo start off, you'll need to get an eColi Culture.\n\nUse that to make the eColi Bacteria Fluid. [note](I know the name is completely different, deal with it)[/note]\n\nCentrifuge that to make Liquid DNA.\n\nAnd combine it with <PERSON><PERSON> in the Mixer to make FluorecentdDNA.\n\neColi Bacteria Fluid can also be Centrifuged for Plasma Membranes (10% chance) and EnzymesSollution, so do that as well.\n\nAnd then Centrifuge the EnzymesSollution again for Polymerase.\n\nYou should expect to need a significant amount of eColi Bacteria Fluid if you plan on copying a lot of cultures, or making the hard to make ones.\n\n[note]To search for items related to copying cultures, search for 'culture', 'dna sample', or 'plasmid'. You can't do this for the Data Orbs, you have to check the uses of DNA Samples.[/note]\n\n§5A bunch of the fluids have incorrect spelling, don't blame me for it.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Breedable:1": 1, "Color:11": [149, 132, 75], "DNA:10": {"Chance:3": 10000, "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Rarity:1": 1, "Tier:3": 0}, "Fluid:8": "escherichiakolifluid", "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Plasmid:10": {"Chance:3": 10000, "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Rarity:1": 1, "Tier:3": 0}, "Rarety:1": 1}}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Fluids for Copying Cultures", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2711, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Breedable:1": 1, "Color:11": [149, 132, 75], "DNA:10": {"Chance:3": 10000, "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Rarity:1": 1, "Tier:3": 0}, "Fluid:8": "escherichiakolifluid", "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Plasmid:10": {"Chance:3": 10000, "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Rarity:1": 1, "Tier:3": 0}, "Rarety:1": 1}}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 11, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.98"}}, "taskID:8": "bq_standard:retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 16, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.98"}, "1:10": {"Count:3": 1, "Damage:2": 13, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.98"}}, "taskID:8": "bq_standard:retrieval"}, "3:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 3, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 6, "OreDict:8": "", "id:8": "bartworks:BioLabParts"}, "1:10": {"Count:3": 1, "Damage:2": 10, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.98"}, "2:10": {"Count:3": 1, "Damage:2": 17, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.98"}}, "taskID:8": "bq_standard:retrieval"}}}