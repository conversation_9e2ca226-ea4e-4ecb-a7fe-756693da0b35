{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1715}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Venus has 3 different veins, with quantium and titanium the most important to grab. And <PERSON><PERSON><PERSON> if you still don't have enough.\n\n[note]You can also get firestone from small ore if you still need it.[/note]\n\n§5Warning! Venus requires a full set of armor with the Spacesuit capability in it or it will kill the player on entering it!!\n\nCheck the space race chapter for more information.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 391, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§b§lVenus Trip", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1717, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 24, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}, "1:10": {"Count:3": 1, "Damage:2": 8, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSpaceII"}, "1:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinAdventureII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 192, "Damage:2": 5391, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 192, "Damage:2": 5028, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 192, "Damage:2": 5324, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}}}