{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2007}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Now that you've started building up your precious Quantum Computer, how about the Research Station? Once again, using the Multiblock Structure Hologram Projector will tell you how to build this Multiblock. There should be 3 empty blocks beneath it so you can see the whole blueprint. Once it is nearly assembled, place down an Energy Hatch, an Optical Reception Connector, and a Maintenance Hatch all on the back side.\n\nOnce fully assembled, you can begin operating recipes at any time, so long as the Quantum Computer is also active and connected. The Research Station requires you to put the item to research within the Object Holder, then a data stick in the Multiblock interface. Once the Computation is fully completed, the data stick that houses all the data of the completed item will be inside the Multiblock Interface.\n\nStill confused? Good, that's the spirit of learning! Always make sure to test these things in creative single player if you wish to find exact measurements and such. Don't forget to ALWAYS COLOR THE OPTICAL HATCHES AND FIBERS!\n\n[note]Note that some researches require a significant amount of power, so keep that in mind.[/note]", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 15331, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Research Station", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2008, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 59, "Damage:2": 1, "OreDict:8": "", "id:8": "tectech:gt.blockcasingsTT"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 15103, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "1:10": {"Count:3": 1, "Damage:2": 15440, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "2:10": {"Count:3": 1, "Damage:2": 15451, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "3:10": {"Count:3": 52, "Damage:2": 1, "OreDict:8": "", "id:8": "tectech:gt.blockcasingsTT"}, "4:10": {"Count:3": 23, "Damage:2": 3, "OreDict:8": "", "id:8": "tectech:gt.blockcasingsTT"}, "5:10": {"Count:3": 14, "Damage:2": 2, "OreDict:8": "", "id:8": "tectech:gt.blockcasingsTT"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 15331, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}}}