---- Minecraft Crash Report ----
// But it works on my machine.

Time: 8/1/25 1:19 PM
Description: Exception in server tick loop

cpw.mods.fml.common.LoaderException: java.lang.NoClassDefFoundError: WayofTime/alchemicalWizardry/compat/structurelib/AltarStructure$AltarMultiblockInfoContainer
	at cpw.mods.fml.common.LoadController.transition(LoadController.java:163)
	at cpw.mods.fml.common.Loader.initializeMods(Loader.java:746)
	at cpw.mods.fml.server.FMLServerHandler.finishServerLoading(FMLServerHandler.java:97)
	at cpw.mods.fml.common.FMLCommonHandler.onServerStarted(FMLCommonHandler.java:323)
	at net.minecraft.server.dedicated.DedicatedServer.func_71197_b(DedicatedServer.java:318)
	at net.minecraft.server.MinecraftServer.run(MinecraftServer.java:644)
	at java.lang.Thread.run(Thread.java:855)
Caused by: java.lang.NoClassDefFoundError: WayofTime/alchemicalWizardry/compat/structurelib/AltarStructure$AltarMultiblockInfoContainer
	at WayofTime.alchemicalWizardry.compat.structurelib.AltarStructure.registerAltarStructureInfo(AltarStructure.java:116)
	at WayofTime.alchemicalWizardry.compat.structurelib.CompatStructureLib.init(CompatStructureLib.java:6)
	at WayofTime.alchemicalWizardry.AlchemicalWizardry.postInit(AlchemicalWizardry.java:3343)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cpw.mods.fml.common.FMLModContainer.handleModStateEvent(FMLModContainer.java:532)
	at sun.reflect.GeneratedMethodAccessor10.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.google.common.eventbus.EventSubscriber.handleEvent(EventSubscriber.java:74)
	at com.google.common.eventbus.SynchronizedEventSubscriber.handleEvent(SynchronizedEventSubscriber.java:47)
	at com.google.common.eventbus.EventBus.dispatch(EventBus.java:322)
	at com.google.common.eventbus.EventBus.dispatchQueuedEvents(EventBus.java:304)
	at com.google.common.eventbus.EventBus.post(EventBus.java:275)
	at cpw.mods.fml.common.LoadController.sendEventToModContainer(LoadController.java:212)
	at cpw.mods.fml.common.LoadController.propogateStateMessage(LoadController.java:190)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.google.common.eventbus.EventSubscriber.handleEvent(EventSubscriber.java:74)
	at com.google.common.eventbus.SynchronizedEventSubscriber.handleEvent(SynchronizedEventSubscriber.java:47)
	at com.google.common.eventbus.EventBus.dispatch(EventBus.java:322)
	at com.google.common.eventbus.EventBus.dispatchQueuedEvents(EventBus.java:304)
	at com.google.common.eventbus.EventBus.post(EventBus.java:275)
	at cpw.mods.fml.common.LoadController.distributeStateMessage(LoadController.java:119)
	at cpw.mods.fml.common.Loader.initializeMods(Loader.java:744)
	... 5 more
Caused by: java.lang.ClassNotFoundException: WayofTime.alchemicalWizardry.compat.structurelib.AltarStructure$AltarMultiblockInfoContainer
	at net.minecraft.launchwrapper.LaunchClassLoader.findClass(LaunchClassLoader.java:193)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:427)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:360)
	... 34 more
Caused by: java.lang.UnsupportedOperationException: Records requires ASM8
	at org.objectweb.asm.ClassVisitor.visit(ClassVisitor.java:120)
	at org.objectweb.asm.commons.RemappingClassAdapter.visit(RemappingClassAdapter.java:71)
	at cpw.mods.fml.common.asm.transformers.deobf.FMLRemappingAdapter.visit(FMLRemappingAdapter.java:36)
	at org.objectweb.asm.ClassReader.accept(ClassReader.java:569)
	at org.objectweb.asm.ClassReader.accept(ClassReader.java:424)
	at cpw.mods.fml.common.asm.transformers.DeobfuscationTransformer.transform(DeobfuscationTransformer.java:37)
	at makamys.coretweaks.optimization.transformerproxy.TransformerProxy.invokeNextHandler(TransformerProxy.java:44)
	at makamys.coretweaks.optimization.transformercache.lite.CachedTransformerWrapper.wrapTransform(CachedTransformerWrapper.java:24)
	at makamys.coretweaks.optimization.transformerproxy.TransformerProxy.invokeNextHandler(TransformerProxy.java:46)
	at makamys.coretweaks.optimization.transformerproxy.TransformerProxy.transform(TransformerProxy.java:27)
	at net.minecraft.launchwrapper.LaunchClassLoader.runTransformers(LaunchClassLoader.java:294)
	at net.minecraft.launchwrapper.LaunchClassLoader.findClass(LaunchClassLoader.java:177)
	... 36 more


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- System Details --
Details:
	Minecraft Version: 1.7.10
	Crucible Version: 1.7.10-staging-6b337850
	Plugins: 
	Disabled Plugins: 
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 1.8.0_442, Alibaba
	Java VM Version: OpenJDK 64-Bit Server VM (mixed mode), Alibaba
	Memory: 1110263768 bytes (1058 MB) / 4261412864 bytes (4064 MB) up to 7604273152 bytes (7252 MB)
	JVM Flags: 0 total; 
	AABB Pool Size: 0 (0 bytes; 0 MB) allocated, 0 (0 bytes; 0 MB) used
	IntCache: cache: 0, tcache: 0, allocated: 0, tallocated: 0
	FML: MCP v9.05 FML v7.10.99.99 Minecraft Forge 10.13.4.1614 286 mods loaded, 286 mods active
	States: 'U' = Unloaded 'L' = Loaded 'C' = Constructed 'H' = Pre-initialized 'I' = Initialized 'J' = Post-initialized 'A' = Available 'D' = Disabled 'E' = Errored
	UCHIJ	mcp{9.05} [Minecraft Coder Pack] (minecraft.jar) 
	UCHIJ	Crucible{1.7.10-staging-6b337850} [Crucible Server] (minecraft.jar) 
	UCHIJ	FML{7.10.99.99} [Forge Mod Loader] (Crucible-1.7.10-staging-6b337850-server_2.jar) 
	UCHIJ	Forge{10.13.4.1614} [Minecraft Forge] (Crucible-1.7.10-staging-6b337850-server_2.jar) 
	UCHIJ	kimagine{0.2} [KImagine] (minecraft.jar) 
	UCHIJ	appliedenergistics2-core{rv3-beta-669-GTNH} [Applied Energistics 2 Core] (minecraft.jar) 
	UCHIJ	CodeChickenCore{1.4.3} [CodeChicken Core] (minecraft.jar) 
	UCHIJ	CropLoadCoreASM{0.0.2} [CroploadCore ASM Core] (minecraft.jar) 
	UCHIJ	NotEnoughItems{2.7.69-GTNH} [NotEnoughItems] (NotEnoughItems-2.7.69-GTNH.jar) 
	UCHIJ	OpenComputers|Core{1.11.16-GTNH} [OpenComputers (Core)] (minecraft.jar) 
	UCHIJ	MobiusCore{1.4.6-mapless} [MobiusCore] (minecraft.jar) 
	UCHIJ	PlayerAPI{1.5.0} [Player API] (minecraft.jar) 
	UCHIJ	ThE-core{1.0.0.1} [Thaumic Energistics Core] (minecraft.jar) 
	UCHIJ	GTNHLib Core{0.6.38} [GTNHLib Core] (minecraft.jar) 
	UCHIJ	ThaumicTinkerer-preloader{0.1} [Thaumic Tinkerer Core] (minecraft.jar) 
	UCHIJ	WitcheryExtras_ASM{0.1-Beta} [WitcheryExtras_ASM] (minecraft.jar) 
	UCHIJ	OpenModsCore{0.10.10} [OpenModsCore] (minecraft.jar) 
	UCHIJ	<CoFH ASM>{000} [CoFH ASM] (minecraft.jar) 
	UCHIJ	unimixins{0.1.22} [UniMixins] (+unimixins-all-1.7.10-0.1.22.jar) 
	UCHIJ	unimixins-mixin{0.1.22} [UniMixins: Mixin (UniMix)] (+unimixins-all-1.7.10-0.1.22.jar) 
	UCHIJ	unimixins-compat{0.1.22} [UniMixins: Compatibility] (+unimixins-all-1.7.10-0.1.22.jar) 
	UCHIJ	mixingasm{0.3} [UniMixins: Mixingasm] (+unimixins-all-1.7.10-0.1.22.jar) 
	UCHIJ	spongemixins{2.0.1} [UniMixins: SpongeMixins] (+unimixins-all-1.7.10-0.1.22.jar) 
	UCHIJ	mixinbooterlegacy{1.2.1} [UniMixins: MixinBooterLegacy] (+unimixins-all-1.7.10-0.1.22.jar) 
	UCHIJ	gasstation{0.5.1} [UniMixins: GasStation] (+unimixins-all-1.7.10-0.1.22.jar) 
	UCHIJ	gtnhmixins{2.2.0} [UniMixins: GTNHMixins] (+unimixins-all-1.7.10-0.1.22.jar) 
	UCHIJ	mixinextras{0.1.22} [UniMixins: MixinExtras] (+unimixins-all-1.7.10-0.1.22.jar) 
	UCHIJ	IC2{2.2.828-experimental} [IndustrialCraft 2] (industrialcraft-2-2.2.828a-experimental.jar) 
	UCHIJ	AdvancedSolarPanel{1.7.10-3.5.1} [Advanced Solar Panels] (AdvancedSolarPanel-1.7.10-3.5.1a.jar) 
	UCHIJ	adventurebackpack{1.3.9-GTNH} [Adventure Backpack] (adventurebackpack-1.3.9-GTNH.jar) 
	UCHIJ	gtnhlib{0.6.38} [GTNH Lib] (gtnhlib-0.6.38.jar) 
	UCHIJ	appliedenergistics2{rv3-beta-669-GTNH} [Applied Energistics 2] (appliedenergistics2-rv3-beta-669-GTNH.jar) 
	UCHIJ	CoFHCore{1.7.10R3.1.4} [CoFH Core] (CoFHCore-[1.7.10]3.1.4-329.jar) 
	UCHIJ	Baubles{2.1.9-GTNH} [Baubles] (BaublesExpanded-2.1.9-GTNH.jar) 
	UCHIJ	Thaumcraft{4.2.3.5} [Thaumcraft] (Thaumcraft-1.7.10-4.2.3.5a.jar) 
	UCHIJ	Waila{1.8.12} [Waila] (Waila-1.8.12.jar) 
	UCHIJ	thaumicenergistics{1.7.11-GTNH} [Thaumic Energistics] (thaumicenergistics-1.7.11-GTNH.jar) 
	UCHIJ	ae2wct{1.12.7} [AE2 Wireless Crafting Terminal] (WirelessCraftingTerminal-1.12.7.jar) 
	UCHIJ	ae2fc{1.4.106-gtnh} [AE2 Fluid Crafting] (ae2fc-1.4.106-gtnh.jar) 
	UCHIJ	ae2noultimatepatterns{1.0.0} [AE2NoUltimatePattern] (ae2noultimatepatterns-1.0.0.jar) 
	UCHIJ	bdlib{1.11.0-GTNH} [BD lib] (bdlib-1.11.0-GTNH.jar) 
	UCHIJ	ae2stuff{0.9.6-GTNH} [AE2 Stuff] (ae2stuff-0.9.6-GTNH.jar) 
	UCHIJ	AFSU{1.3.1-GTNH} [AFSU Mod] (AFSU-1.3.1-GTNH.jar) 
	UCHIJ	akashictome{1.2.6} [Akashic Tome] (akashictome-1.2.6.jar) 
	UCHIJ	alchgrate{1.3.1-GTNH} [Alchemical Grate] (alchgrate-1.3.1-GTNH.jar) 
	UCHIJ	amazingtrophies{1.3.8} [Amazing Trophies] (amazingtrophies-1.3.8.jar) 
	UCHIJ	BuildCraft|Core{7.1.44} [BuildCraft] (buildcraft-7.1.44.jar) 
	UCHIJ	BuildCraft|Energy{7.1.44} [BC Energy] (buildcraft-7.1.44.jar) 
	UCHIJ	Mantle{0.5.1} [Mantle] (Mantle-0.5.1.jar) 
	UCHIJ	battlegear2{1.5.5-backhand} [Mine & Blade Battlegear 2] (battlegear2-1.5.5-backhand.jar) 
	UCHIJ	ForgeMultipart{1.6.7} [Forge Multipart] (ForgeMultipart-1.6.7.jar) 
	UCHIJ	ExtraUtilities{1.2.12} [Extra Utilities] (extrautilities-1.2.13a.jar) 
	UCHIJ	TConstruct{1.13.46-GTNH} [Tinkers' Construct] (TConstruct-1.13.46-GTNH.jar) 
	UCHIJ	GalacticraftCore{3.3.10-GTNH} [Galacticraft Core] (Galacticraft-3.3.10-GTNH.jar) 
	UCHIJ	GalacticraftMars{3.3.10-GTNH} [Galacticraft Planets] (Galacticraft-3.3.10-GTNH.jar) 
	UCHIJ	backhand{1.6.38} [Backhand] (backhand-1.6.38.jar) 
	UCHIJ	endercore{0.4.8} [EnderCore] (endercore-0.4.8.jar) 
	UCHIJ	Natura{2.8.8} [Natura] (Natura-2.8.8.jar) 
	UCHIJ	BiomesOPlenty{2.1.0} [Biomes O' Plenty] (BiomesOPlenty-1.7.10-2.1.0.2308-universal.jar) 
	UCHIJ	HardcoreEnderExpansion{1.12.13-GTNH} [Hardcore Ender Expansion] (HardcoreEnderExpansion-1.12.13-GTNH.jar) 
	UCHIJ	Forestry{4.10.16} [Forestry] (Forestry-4.10.16.jar) 
	UCHIJ	BuildCraft|Builders{7.1.44} [BC Builders] (buildcraft-7.1.44.jar) 
	UCHIJ	BuildCraft|Factory{7.1.44} [BC Factory] (buildcraft-7.1.44.jar) 
	UCHIJ	Railcraft{9.16.32} [Railcraft] (Railcraft-9.16.32.jar) 
	UCHIJ	EnderIO{2.9.22} [Ender IO] (EnderIO-2.9.22.jar) 
	UCHIJ	supersolarpanel{1.1.4} [Super Solar Panel] (supersolarpanel-1.1.4.jar) 
	UCHIJ	YAMCore{0.7.1} [YAMCore] (YAMCore-0.7.1.jar) 
	UCHIJ	dreamcraft{2.7.232} [GT: New Horizons] (GTNewHorizonsCoreMod-2.7.232.jar) 
	UCHIJ	IronChest{6.1.6} [Iron Chests] (IronChest-6.1.6.jar) 
	UCHIJ	GalacticraftAmunRa{0.8.2} [Amun-Ra] (AmunRa-GC-0.8.2.jar) 
	UCHIJ	angermod{0.9.0} [AngerMod. Makes your Mobs angry!] (AngerMod-0.9.0.jar) 
	UCHIJ	AppleCore{3.3.5} [AppleCore] (AppleCore-3.3.5.jar) 
	UCHIJ	ArchitectureCraft{1.11.2} [ArchitectureCraft] (ArchitectureCraft-1.11.2.jar) 
	UCHIJ	asielib{0.7.0} [AsieLib] (AsieLib-0.7.0.jar) 
	UCHIJ	Automagy{0.28.2} [Automagy] (Automagy-1.7.10-0.28.2.jar) 
	UCHIE	AWWayofTime{1.7.48} [Blood Magic: Alchemical Wizardry] (BloodMagic-1.7.48.jar) 
	UCHIJ	Botania{1.12.21-GTNH} [Botania] (Botania-1.12.21-GTNH.jar) 
	UCHIJ	Avaritia{1.70} [Avaritia] (Avaritia-1.70.jar) 
	UCHIJ	wanionlib{1.10.0} [WanionLib] (WanionLib-1.10.0.jar) 
	UCHIJ	avaritiaddons{1.9.0-GTNH} [Avaritiaddons] (Avaritiaddons-1.9.0-GTNH.jar) 
	UCHIJ	Backpack{2.5.4-GTNH} [Backpack Editted for ModdedNetwork] (backpack-2.5.4-GTNH.jar) 
	UCHIJ	Baubles|Expanded{2.1.9-GTNH} [Baubles Expanded] (BaublesExpanded-2.1.9-GTNH.jar) 
	UCHIJ	betterbuilderswands{0.13.3-GTNH} [BetterBuildersWands] (BetterBuildersWands-0.13.3-GTNH.jar) 
	UCHIJ	forgelin{2.0.3-GTNH} [Forgelin] (forgelin-2.0.3-GTNH.jar) 
	UCHIJ	betterp2p{1.3.1} [BetterP2P] (betterp2p-1.3.1.jar) 
	UCHIJ	betterquesting{3.7.11-GTNH} [BetterQuesting] (BetterQuesting-3.7.11-GTNH.jar) 
	UCHIJ	bq_standard{3.7.11-GTNH} [Standard Expansion] (BetterQuesting-3.7.11-GTNH.jar) 
	UCHIJ	cb4bq{3.7.11-GTNH} [Command Blocks for Better Questing] (BetterQuesting-3.7.11-GTNH.jar) 
	UCHIJ	questbook{3.7.11-GTNH} [BetterQuesting] (BetterQuesting-3.7.11-GTNH.jar) 
	UCHIJ	BiblioCraft{1.11.7} [BiblioCraft] (BiblioCraft[v1.11.7][MC1.7.10].jar) 
	UCHIJ	BiblioWoodsBoP{1.9} [BiblioWoods Biomes O'Plenty Edition] (BiblioWoods[BiomesOPlenty][v1.9].jar) 
	UCHIJ	BiblioWoodsForestry{1.7} [BiblioWoods Forestry Edition] (BiblioWoods[Forestry][v1.7].jar) 
	UCHIJ	BiblioWoodsNatura{1.5} [BiblioWoods Natura Edition] (BiblioWoods[Natura][v1.5].jar) 
	UCHIJ	BinnieCore{2.5.16} [Binnie Core] (binnie-mods-2.5.16.jar) 
	UCHIJ	Botany{2.5.16} [Botany] (binnie-mods-2.5.16.jar) 
	UCHIJ	Genetics{2.5.16} [Genetics] (binnie-mods-2.5.16.jar) 
	UCHIJ	ExtraTrees{2.5.16} [Extra Trees] (binnie-mods-2.5.16.jar) 
	UCHIJ	ExtraBees{2.5.16} [Extra Bees] (binnie-mods-2.5.16.jar) 
	UCHIJ	blocklimiter{0.7.0} [BlockLimiter. Restrict your Blocks] (BlockLimiter-0.7.0.jar) 
	UCHIJ	structurelib{1.4.15} [StructureLib] (structurelib-1.4.15.jar) 
	UCHIJ	blockrenderer6343{1.3.16} [BlockRenderer6343] (blockrenderer6343-1.3.16.jar) 
	UCHIJ	ThaumicTinkerer{2.11.20} [Thaumic Tinkerer] (ThaumicTinkerer-2.11.20.jar) 
	UCHIJ	ForbiddenMagic{0.8.1-GTNH} [Forbidden Magic] (Forbidden.Magic-0.8.1-GTNH.jar) 
	UCHIE	BloodArsenal{1.4.10} [Blood Arsenal] (BloodArsenal-1.4.10.jar) 
	UCHIJ	craftingtweaks{1.2.51-GTNH} [Gregory Tweaks For Crafting] (bogosorter-1.2.51-GTNH.jar) 
	UCHIJ	MouseTweaks{1.2.51-GTNH} [Mouse Tweaks Unofficial] (bogosorter-1.2.51-GTNH.jar) 
	UCHIJ	hodgepodge{2.6.96} [Hodgepodge] (hodgepodge-2.6.96.jar) 
	UCHIJ	modularui2{2.2.16-1.7.10} [Modular UI 2] (modularui2-2.2.16-1.7.10.jar) 
	UCHIJ	bogosorter{1.2.51-GTNH} [Inventory Bogo Sorter] (bogosorter-1.2.51-GTNH.jar) 
	UCHIJ	TwilightForest{2.7.8} [The Twilight Forest] (TwilightForest-2.7.8.jar) 
	UCHIJ	chisel{2.16.8-GTNH} [Chisel] (chisel-2.16.8-GTNH.jar) 
	UCHIJ	BuildCraft|Silicon{7.1.44} [BC Silicon] (buildcraft-7.1.44.jar) 
	UCHIJ	BuildCraft|Transport{7.1.44} [BC Transport] (buildcraft-7.1.44.jar) 
	UCHIJ	MagicBees{2.9.4-GTNH} [Magic Bees] (magicbees-2.9.4-GTNH.jar) 
	UCHIJ	gendustry{1.9.4-GTNH} [Gendustry] (gendustry-1.9.4-GTNH.jar) 
	UCHIJ	ggfab{5.09.51.418} [GigaGramFab] (gregtech-5.09.51.418.jar) 
	UCHIJ	harvestcraft{1.3.2-GTNH} [Pam's HarvestCraft] (harvestcraft-1.3.2-GTNH.jar) 
	UCHIJ	modularui{1.2.20} [ModularUI] (modularui-1.2.20.jar) 
	UCHIJ	Translocator{1.3.1} [Translocator] (Translocator-1.3.1.jar) 
	UCHIJ	gregtech{MC1710} [GregTech] (gregtech-5.09.51.418.jar) 
	UCHIJ	witchery{0.24.1} [Witchery] (witchery-1.7.10-0.24.1.jar) 
	UCHIJ	botanichorizons{1.11.18-GTNH} [botanichorizons] (BotanicHorizons-1.11.18-GTNH.jar) 
	UCHIJ	BrandonsCore{1.2.0-GTNH} [Brandon's Core] (BrandonsCore-1.2.0-GTNH.jar) 
	UCHIJ	bugtorch{1.2.14} [BugTorch] (bugtorch-1.2.14.jar) 
	UCHIJ	BuildCraft|Robotics{7.1.44} [BC Robotics] (buildcraft-7.1.44.jar) 
	UCHIJ	BuildCraft|Compat{7.1.18} [BuildCraft Compat] (buildcraft-compat-7.1.18.jar) 
	UCHIJ	OilTweak{1.1.1} [BuildCraft Oil Tweak] (BuildCraftOilTweak-1.1.1.jar) 
	UCHIJ	CarpentersBlocks{3.7.0-GTNH} [Carpenter's Blocks] (CarpentersBlocks-3.7.0-GTNH.jar) 
	UCHIJ	catwalks{2.0.4} [Catwalks Mod] (catwalks-2.4.0-GTNH.jar) 
	UCHIJ	Ztones{1.7.10} [Ztones] (Ztones-1.7.10-2.2.2.jar) 
	UCHIJ	chiseltones{1.2.0-GTNH} [Chisel Tones] (chiseltones-1.2.0-GTNH.jar) 
	UCHIJ	compactkineticgenerators{1.7.10-1.0} [Compact Kinetic Wind and Water Generators] (CompactKineticGenerators-1.7.10-1.0.jar) 
	UCHIJ	EnderStorage{1.7.5} [EnderStorage] (EnderStorage-1.7.5.jar) 
	UCHIJ	MrTJPCoreMod{1.3.1} [MrTJPCore] (MrTJPCore-1.3.1.jar) 
	UCHIJ	ProjRed|Core{4.11.8-GTNH} [ProjectRed Core] (ProjRed-4.11.8-GTNH.jar) 
	UCHIJ	ProjRed|Transmission{4.11.8-GTNH} [ProjectRed Transmission] (ProjRed-4.11.8-GTNH.jar) 
	UCHIJ	OpenComputers{1.11.16-GTNH} [OpenComputers] (OpenComputers-1.11.16-GTNH.jar) 
	UCHIJ	computronics{1.9.3-GTNH} [Computronics] (Computronics-1.9.3-GTNH.jar) 
	UCHIJ	cookingforblockheads{1.4.4-GTNH} [Cooking For Blockheads] (CookingForBlockheads-1.4.4-GTNH.jar) 
	UCHIJ	coretweaks{0.3.3.6-GTNH} [CoreTweaks] (coretweaks-0.3.3.6-GTNH.jar) 
	UCHIJ	MineTweaker3{3.4.2} [MineTweaker 3] (CraftTweaker-3.4.2.jar) 
	UCHIJ	DummyCore{1.19.0} [DummyCore] (DummyCore-1.19.0.jar) 
	UCHIJ	thaumicbases{1.8.11} [Thaumic Bases] (Thaumic-Based-1.8.11.jar) 
	UCHIJ	croploadcore{0.2.0} [CropLoadCore] (CropLoadCore-0.2.0.jar) 
	UCHIJ	berriespp{1.8.10} [Crops++] (CropsPP-1.8.10.jar) 
	UCHIJ	darkerer{1.0.6} [Darkerer] (darkerer-1.0.6.jar) 
	UCHIJ	DraconicEvolution{1.4.24-GTNH} [Draconic Evolution] (Draconic-Evolution-1.4.24-GTNH.jar) 
	UCHIJ	EMT{1.6.11} [Electro-Magic Tools] (EMT-1.6.11.jar) 
	UCHIJ	EnderZoo{1.3.3} [Ender Zoo] (EnderZoo-1.3.3.jar) 
	UCHIJ	enhancedlootbags{1.2.8} [Enhanced LootBags] (EnhancedLootBags-1.2.8.jar) 
	UCHIJ	universalsingularities{8.10.0} [UniversalSingularities] (Universal-Singularities-8.10.0.jar) 
	UCHIJ	eternalsingularity{1.2.1} [Eternal Singularity] (eternalsingularity-1.2.1.jar) 
	UCHIJ	etfuturum{********-GTNH-daily} [Et Futurum Requiem] (etfuturum-********-GTNH-daily.jar) 
	UCHIJ	findit{1.4.0} [FindIt] (findit-1.4.0.jar) 
	UCHIJ	FloodLights{1.5.4} [Flood Lights] (FloodLights-1.5.4.jar) 
	UCHIJ	ForgeMicroblock{1.6.7} [Forge Microblocks] (ForgeMultipart-1.6.7.jar) 
	UCHIJ	McMultipart{1.6.7} [Minecraft Multipart Plugin] (ForgeMultipart-1.6.7.jar) 
	UCHIJ	ForgeRelocation{0.3.3} [ForgeRelocation] (ForgeRelocation-0.3.3.jar) 
	UCHIJ	MCFrames{0.3.3} [MCFrames] (ForgeRelocation-0.3.3.jar) 
	UCHIJ	RelocationFMP{0.2.0} [RelocationFMP] (ForgeRelocationFMP-0.2.0.jar) 
	UCHIJ	gadomancy{1.4.7} [Gadomancy] (gadomancy-1.4.7.jar) 
	UCHIJ	GalaxySpace{1.1.116-GTNH} [GalaxySpace] (GalaxySpace-1.1.116-GTNH.jar) 
	UCHIJ	GraviSuite{1.7.10-2.0.3} [Graviation Suite] (GraviSuite-1.7.10-2.0.3.jar) 
	UCHIJ	gravisuiteneo{1.3.6} [Gravitation Suite Neo] (gravisuiteneo-1.3.6.jar) 
	UCHIJ	galacticgreg{5.09.51.418} [Galactic Greg] (gregtech-5.09.51.418.jar) 
	UCHIJ	gtneioreplugin{5.09.51.418} [GT NEI Ore Plugin GT:NH Mod] (gregtech-5.09.51.418.jar) 
	UCHIJ	tectech{5.09.51.418} [TecTech - Tec Technology!] (gregtech-5.09.51.418.jar) 
	UCHIJ	ProjRed|Illumination{4.11.8-GTNH} [ProjectRed Illumination] (ProjRed-4.11.8-GTNH.jar) 
	UCHIJ	RandomThings{2.6.6} [RandomThings] (RandomThings-2.6.6.jar) 
	UCHIJ	bartworks{5.09.51.418} [BartWorks] (gregtech-5.09.51.418.jar) 
	UCHIJ	GoodGenerator{5.09.51.418} [Good Generator] (gregtech-5.09.51.418.jar) 
	UCHIJ	gtnhlanth{5.09.51.418} [GTNH: Lanthanides] (gregtech-5.09.51.418.jar) 
	UCHIJ	bartworkscrossmodtgregworkscontainer{5.09.51.418} [BartWorks Mod Additions - TGregworks Container] (gregtech-5.09.51.418.jar) 
	UCHIJ	gregtech_nh{5.09.51.418} [GregTech GTNH] (gregtech-5.09.51.418.jar) 
	UCHIJ	IC2NuclearControl{2.6.18} [Nuclear Control 2] (IC2NuclearControl-2.6.18.jar) 
	UCHIJ	OpenMods{0.10.10} [OpenMods] (OpenModsLibs-0.10.10.jar) 
	UCHIJ	OpenBlocks{1.11.7-GTNH} [OpenBlocks] (OpenBlocks-1.11.7-GTNH.jar) 
	UCHIJ	StevesCarts{2.3.11} [Steve's Carts 2] (StevesCarts-2.3.11.jar) 
	UCHIJ	TGregworks{1.7.10-GTNH-1.0.28} [Tinkers' Gregworks] (TGregworks-1.7.10-GTNH-1.0.28.jar) 
	UCHIJ	miscutils{5.09.51.418} [GT++] (gregtech-5.09.51.418.jar) 
	UCHIJ	ToxicEverglades{5.09.51.418} [GregTech ToxicEverglades] (gregtech-5.09.51.418.jar) 
	UCHIJ	InfernalMobs{1.10.2-GTNH} [Infernal Mobs] (InfernalMobs-1.10.2-GTNH.jar) 
	UCHIJ	mobsinfo{0.5.3-GTNH} [MobsInfo] (mobsinfo-0.5.3-GTNH.jar) 
	UCHIJ	kubatech{5.09.51.418} [KubaTech] (gregtech-5.09.51.418.jar) 
	UCHIJ	openmodularturrets{2.4.3} [Open Modular Turrets] (OpenModularTurrets-2.4.3.jar) 
	UCHIJ	gtnhintergalactic{5.09.51.418} [GTNH-Intergalactic] (gregtech-5.09.51.418.jar) 
	UCHIJ	detravscannermod{5.09.51.418} [GT Scanner Mod] (gregtech-5.09.51.418.jar) 
	UCHIJ	kekztech{5.09.51.418} [KekzTech] (gregtech-5.09.51.418.jar) 
	UCHIJ	bartworkscrossmod{5.09.51.418} [BartWorks Mod Additions] (gregtech-5.09.51.418.jar) 
	UCHIJ	TaintedMagic{r7.6} [Tainted Magic] (Tainted-Magic-7.6.23-GTNH.jar) 
	UCHIJ	ThaumicExploration{1.4.2-GTNH} [Thaumic Exploration] (Thaumic-Exploration-1.4.2-GTNH.jar) 
	UCHIE	gtnhtcwands{1.4.6} [GTNH-TC-Wands] (GTNH-TC-Wands-1.4.6.jar) 
	UCHIJ	GTTweaker{2.3.1} [GTTweaker] (GTTweaker-2.3.1.jar) 
	UCHIJ	HelpFixer{1.3.0} [HelpFixer] (HelpFixer-1.3.0.jar) 
	UCHIJ	holoinventory{2.5.4-GTNH} [Holo Inventory] (holoinventory-2.5.4-GTNH.jar) 
	UCHIJ	hydroenergy{1.4.9} [HydroEnergy] (hydroenergy-1.4.9.jar) 
	UCHIJ	ifu{1.11.1} [I Will Find You] (ifu-1.11.1.jar) 
	UCHIJ	LunatriusCore{1.2.1-GTNH} [LunatriusCore] (LunatriusCore-1.2.1-GTNH.jar) 
	UCHIJ	InGameInfoXML{2.8.28} [InGame Info XML] (InGameInfoXML-2.8.28.jar) 
	UCHIJ	ironchestminecarts{1.2.0} [Iron Chest Minecarts] (IronChestMinecarts-1.2.0.jar) 
	UCHIJ	irontank{1.4.2} [Iron Tanks] (irontanks-1.4.2.jar) 
	UCHIJ	irontankminecarts{1.0.9} [Iron Tank Minecarts] (irontankminecarts-1.0.9.jar) 
	UCHIJ	JABBA{1.5.10} [JABBA] (JABBA-1.5.10.jar) 
	UCHIJ	JourneyMapServer{1.0.5_MC1.7.10} [JourneyMapServer] (JourneyMapServer1.0.5_MC1.7.10.jar) 
	UCHIJ	littletiles{1.5.11-GTNH} [LittleTiles] (littletiles-1.5.11-GTNH.jar) 
	UCHIJ	creativecore{1.5.11-GTNH} [CreativeCore] (littletiles-1.5.11-GTNH.jar) 
	UCHIJ	LogisticsPipes{1.4.22-GTNH} [Logistics Pipes] (logisticspipes-1.4.22-GTNH.jar) 
	UCHIJ	lootgames{2.2.0} [LootGames] (lootgames-2.2.0.jar) 
	UCHIJ	malisiscore{1.18.2-GTNH} [Malisis Core] (malisisdoors-1.18.2-GTNH.jar) 
	UCHIJ	malisisdoors{1.18.2-GTNH} [Malisis' Doors] (malisisdoors-1.18.2-GTNH.jar) 
	UCHIJ	matter-manipulator{0.0.39-GTNH} [Matter Manipulator] (matter-manipulator-0.0.39-GTNH.jar) 
	UCHIJ	modernmarkings{0.3.12-1.7.10} [ModernMarkings] (modernmarkings-0.3.12-1.7.10.jar) 
	UCHIJ	modtweaker2{0.12.0} [Mod Tweaker 2] (ModTweaker2-0.12.0.jar) 
	UCHIJ	Morpheus{1.7.10-1.6.21} [Morpheus] (Morpheus-1.7.10-1.6.21.jar) 
	UCHIJ	naturescompass{1.5.0-GTNH} [Nature's Compass] (naturescompass-1.5.0-GTNH.jar) 
	UCHIJ	navigator{1.0.17} [Navigator] (navigator-1.0.17.jar) 
	UCHIJ	NEIAddons{1.16.0} [NEI Addons] (NEIAddons-1.16.0.jar) 
	UCHIJ	NEIAddons|Forestry{1.16.0} [NEI Addons: Forestry] (NEIAddons-1.16.0.jar) 
	UCHIJ	NEIAddons|Developer{1.16.0} [NEI Addons: Developer Tools] (NEIAddons-1.16.0.jar) 
	UCHIJ	NEIAddons|AppEng{1.16.0} [NEI Addons: Applied Energistics 2] (NEIAddons-1.16.0.jar) 
	UCHIJ	NEIAddons|Botany{1.16.0} [NEI Addons: Botany] (NEIAddons-1.16.0.jar) 
	UCHIJ	NEIAddons|ExNihilo{1.16.0} [NEI Addons: Ex Nihilo] (NEIAddons-1.16.0.jar) 
	UCHIJ	NEIAddons|CraftingTables{1.16.0} [NEI Addons: Crafting Tables] (NEIAddons-1.16.0.jar) 
	UCHIJ	neicustomdiagram{1.7.5} [NEI Custom Diagram] (NEICustomDiagram-1.7.5.jar) 
	UCHIJ	neiintegration{1.5.0} [NEIIntegration] (NEIIntegration-1.5.0.jar) 
	UCHIJ	netherportalfix{1.4.0} [Nether Portal Fix] (netherportalfix-1.4.0.jar) 
	UCHIE	NodalMechanics{1.3.1-GTNH} [NodalMechanics] (NodalMechanics-1.3.1-GTNH.jar) 
	UCHIJ	neenergistics{1.7.7} [NotEnoughEnergistics] (NotEnoughEnergistics-1.7.7.jar) 
	UCHIJ	neid{2.1.10} [NotEnoughIDs] (notenoughIDs-2.1.10.jar) 
	UCHIJ	nutrition{0.1.3} [Nutrition] (nutrition-0.1.3.jar) 
	UCHIJ	openglasses{1.6.1-GTNH} [OC Glasses] (OpenGlasses-1.6.1-GTNH.jar) 
	UCHIJ	openprinter{0.3.0-GTNH} [OpenPrinter] (openprinter-0.3.0-GTNH.jar) 
	UCHIJ	opensecurity{1.2.0-GTNH} [OpenSecurity] (opensecurity-1.2.0-GTNH.jar) 
	UCHIJ	Opis{1.4.6-mapless} [Opis] (Opis-1.4.6-mapless.jar) 
	UCHIJ	harvestthenether{1.7.10} [Pam's Harvest the Nether] (Pam's Harvest the Nether 1.7.10a.jar) 
	UCHIJ	personalspace{1.0.33} [PersonalSpace] (personalspace-1.0.33.jar) 
	UCHIJ	postea{1.1.3} [Postea] (postea-1.1.3.jar) 
	UCHIJ	ProjectBlue{1.2.0-GTNH} [Project Blue] (ProjectBlue-1.2.0-GTNH.jar) 
	UCHIJ	ProjRed|Integration{4.11.8-GTNH} [ProjectRed Integration] (ProjRed-4.11.8-GTNH.jar) 
	UCHIJ	ProjRed|Fabrication{4.11.8-GTNH} [ProjectRed Fabrication] (ProjRed-4.11.8-GTNH.jar) 
	UCHIJ	ProjRed|Transportation{4.11.8-GTNH} [ProjectRed Transportation] (ProjRed-4.11.8-GTNH.jar) 
	UCHIJ	ProjRed|Exploration{4.11.8-GTNH} [ProjectRed Exploration] (ProjRed-4.11.8-GTNH.jar) 
	UCHIJ	ProjRed|Compatibility{4.11.8-GTNH} [ProjectRed Compatibility] (ProjRed-4.11.8-GTNH.jar) 
	UCHIJ	ProjRed|Expansion{4.11.8-GTNH} [ProjectRed Expansion] (ProjRed-4.11.8-GTNH.jar) 
	UCHIJ	RIO{2.7.6} [RemoteIO] (RemoteIO-2.7.6.jar) 
	UCHIJ	Roguelike{1.6.6-GTNH} [Roguelike Dungeons] (roguelike-1.6.6-GTNH.jar) 
	UCHIJ	RWG{alpha-1.5.0} [Realistic World Gen Alpha] (RWG-alpha-1.5.0.jar) 
	UCHIJ	tc4tweak{1.5.35} [TC4 Tweak] (Thaumcraft4Tweaks-1.5.35.jar) 
	UCHIJ	salisarcana{1.1.25-GTNH} [Salis Arcana] (salisarcana-1.1.25-GTNH.jar) 
	UCHIJ	serverutilities{2.1.57} [ServerUtilities] (ServerUtilities-2.1.57.jar) 
	UCHIJ	SGCraft{1.4.5-GTNH} [SG Craft] (SGCraft-1.4.5-GTNH.jar) 
	UCHIJ	sharewhereiam{2.1.4} [Share Where I am] (sharewhereiam-2.1.4.jar) 
	UCHIJ	sleepingbag{0.3.0} [Sleeping Bag] (sleepingbag-0.3.0.jar) 
	UCHIJ	SpecialMobs{3.7.0} [Special Mobs] (SpecialMobs-3.7.0.jar) 
	UCHIJ	SpiceOfLife{2.2.2-carrot} [The Spice of Life - Carrot Edition] (SpiceOfLife-2.2.2-carrot.jar) 
	UCHIJ	StevesFactoryManager{1.3.4-GTNH} [Steve's Factory Manager] (StevesFactoryManager-1.3.4-GTNH.jar) 
	UCHIJ	StevesAddons{0.14.2} [Steve's Addons] (StevesAddons-0.14.2.jar) 
	UCHIJ	StorageDrawers{2.1.6-GTNH} [Storage Drawers] (StorageDrawers-2.1.6-GTNH.jar) 
	UCHIJ	StorageDrawersMisc{2.1.6-GTNH} [Storage Drawers: Misc Pack] (StorageDrawers-2.1.6-GTNH.jar) 
	UCHIJ	StorageDrawersBop{2.1.6-GTNH} [Storage Drawers: Biomes O' Plenty Pack] (StorageDrawers-2.1.6-GTNH.jar) 
	UCHIJ	StorageDrawersNatura{2.1.6-GTNH} [Storage Drawers: Natura Pack] (StorageDrawers-2.1.6-GTNH.jar) 
	UCHIJ	StorageDrawersErebus{2.1.6-GTNH} [Storage Drawers: Erebus Pack Pack] (StorageDrawers-2.1.6-GTNH.jar) 
	UCHIJ	StorageDrawersForestry{2.1.6-GTNH} [Storage Drawers: Forestry Pack] (StorageDrawers-2.1.6-GTNH.jar) 
	UCHIJ	structurecompat{0.7.2} [StructureCompat] (structurecompat-0.7.2.jar) 
	UCHIJ	SuperTic{1.5.0} [SuperTic] (SuperTic-1.5.0.jar) 
	UCHIJ	ThaumcraftMobAspects{1.2.0-GTNH} [Thaumcraft Mob Aspects] (ThaumcraftMobAspects-1.2.0-GTNH.jar) 
	UCHIJ	ThaumcraftResearchTweaks{1.3.0} [Thaumcraft Research Tweaks] (ThaumcraftResearchTweaks-1.3.0.jar) 
	UCHIJ	ThaumicMachina{0.2.1} [Thaumic Machina] (Thaumic Machina-1.7.10-0.2.1.jar) 
	UCHIJ	thaumicboots{1.4.11} [Thaumic Boots] (thaumicboots-1.4.11.jar) 
	UCHIJ	ThaumicHorizons{1.7.6} [ThaumicHorizons] (ThaumicHorizons-1.7.6.jar) 
	UCHIJ	thaumicinsurgence{0.4.0} [Thaumic Insurgence] (thaumicinsurgence-0.4.0.jar) 
	UCHIJ	tinkersdefense{1.3.2} [Tinkers Defense] (tinkersdefense-1.3.2.jar) 
	UCHIJ	TMechworks{0.4.1} [Tinkers Mechworks] (TMechworks-0.4.1.jar) 
	UCHIJ	TML{4.3.0-GTNH} [TooMuchLoot] (TooMuchLoot-4.3.0-GTNH.jar) 
	UCHIJ	visualprospecting{1.4.6} [VisualProspecting] (visualprospecting-1.4.6.jar) 
	UCHIJ	WailaHarvestability{1.3.4-GTNH} [Waila Harvestability] (WailaHarvestability-1.3.4-GTNH.jar) 
	UCHIJ	wailaplugins{0.6.0} [WAILA Plugins] (WAILAPlugins-0.6.0.jar) 
	UCHIJ	WarpTheory{1.5.0-GTNH} [WarpTheory] (WarpTheory-1.5.0-GTNH.jar) 
	UCHIJ	wawla{1.3.1-GTNH} [What Are We Looking At] (Wawla-1.3.1-GTNH.jar) 
	UCHIJ	WitcheryExtras{1.3.1} [Witchery++] (WitcheryExtras-1.3.1.jar) 
	UCHIJ	WitchingGadgets{1.7.16-GTNH} [Witching Gadgets] (WitchingGadgets-1.7.16-GTNH.jar) 
	UCHIJ	WR-CBE|Core{1.7.1} [WR-CBE Core] (WR-CBE-1.7.1.jar) 
	UCHIJ	WR-CBE|Logic{1.7.1} [WR-CBE Logic] (WR-CBE-1.7.1.jar) 
	UCHIJ	WR-CBE|Addons{1.7.1} [WR-CBE Addons] (WR-CBE-1.7.1.jar) 
	UCHIJ	HungerOverhaul{1.7.10-1.0.0.jenkins104} [Hunger Overhaul] (HungerOverhaul-1.7.10-1.0.0.jenkins104.jar) 
	UCHIJ	IguanaTweaksTConstruct{2.6.5} [Iguana Tinker Tweaks] (IguanaTweaksTConstruct-2.6.5.jar) 
	OpenModsLib class transformers: [stencil_patches:ENABLED],[movement_callback:ENABLED],[player_damage_hook:ACTIVATED],[map_gen_fix:FINISHED],[gl_capabilities_hook:ENABLED],[player_render_hook:ENABLED]
	Class transformer null safety: all safe
	AE2 Version: rv3-beta-669-GTNH for Forge 10.13.4.1614
	CoFHCore: -[1.7.10]3.1.4-329
	Mantle Environment: DO NOT REPORT THIS CRASH! Unsupported mods in environment: bukkit
	CPU Threads: 12
	TC4Tweak signing signature: 473C3A397676978FF4877ABA2D57860DDA20E2FC, Built by: glease
	List of loaded APIs: 
		* ae2wct|API (1.7.10-rv3-1.8.6b) from WirelessCraftingTerminal-1.12.7.jar
		* Amazing Trophies API (1.3.8) from amazingtrophies-1.3.8.jar
		* AppleCoreAPI (3.3.5) from AppleCore-3.3.5.jar
		* appliedenergistics2|API (rv3) from appliedenergistics2-rv3-beta-669-GTNH.jar
		* asielibAPI (1.1) from AsieLib-0.7.0.jar
		* asielibAPI|chat (1.0) from AsieLib-0.7.0.jar
		* asielibAPI|tile (1.0) from AsieLib-0.7.0.jar
		* asielibAPI|tool (1.1) from AsieLib-0.7.0.jar
		* BattlePlayer (0.1) from battlegear2-1.5.5-backhand.jar
		* Baubles|API (2.1.2) from BaublesExpanded-2.1.9-GTNH.jar
		* BetterQuesting|API (3.2) from BetterQuesting-3.7.11-GTNH.jar
		* BetterQuesting|API2 (3.1) from BetterQuesting-3.7.11-GTNH.jar
		* BiomesOPlentyAPI (1.0.0) from BiomesOPlenty-1.7.10-2.1.0.2308-universal.jar
		* BloodMagicAPI (1.3.3-13) from BloodMagic-1.7.48.jar
		* BotaniaAPI (76) from Botania-1.12.21-GTNH.jar
		* BuildCraftAPI|blocks (1.0) from buildcraft-7.1.44.jar
		* BuildCraftAPI|blueprints (1.5) from buildcraft-7.1.44.jar
		* BuildCraftAPI|boards (2.0) from buildcraft-7.1.44.jar
		* BuildCraftAPI|core (2.0) from buildcraft-7.1.44.jar
		* BuildCraftAPI|crops (1.1) from buildcraft-7.1.44.jar
		* BuildCraftAPI|events (2.0) from buildcraft-7.1.44.jar
		* BuildCraftAPI|facades (1.1) from buildcraft-7.1.44.jar
		* BuildCraftAPI|filler (4.0) from buildcraft-7.1.44.jar
		* BuildCraftAPI|fuels (2.0) from buildcraft-7.1.44.jar
		* BuildCraftAPI|gates (4.1) from buildcraft-7.1.44.jar
		* BuildCraftAPI|items (1.1) from buildcraft-7.1.44.jar
		* BuildCraftAPI|library (2.0) from buildcraft-7.1.44.jar
		* BuildCraftAPI|lists (1.0) from buildcraft-7.1.44.jar
		* BuildCraftAPI|power (1.3) from buildcraft-7.1.44.jar
		* BuildCraftAPI|recipes (3.1) from buildcraft-7.1.44.jar
		* BuildCraftAPI|robotics (3.0) from buildcraft-7.1.44.jar
		* BuildCraftAPI|statements (1.1) from buildcraft-7.1.44.jar
		* BuildCraftAPI|tablet (1.0) from buildcraft-7.1.44.jar
		* BuildCraftAPI|tiles (1.2) from buildcraft-7.1.44.jar
		* BuildCraftAPI|tools (1.0) from buildcraft-7.1.44.jar
		* BuildCraftAPI|transport (4.1) from buildcraft-7.1.44.jar
		* CarpentersBlocks|API (3.3.7) from CarpentersBlocks-3.7.0-GTNH.jar
		* ChiselAPI (0.1.1) from chisel-2.16.8-GTNH.jar
		* ChiselAPI|Carving (0.1.1) from chisel-2.16.8-GTNH.jar
		* ChiselAPI|Rendering (0.1.1) from chisel-2.16.8-GTNH.jar
		* CoFHAPI (1.7.10R1.0.2) from RandomThings-2.6.6.jar
		* CoFHAPI|block (1.7.10R1.0.13B1) from extrautilities-1.2.13a.jar
		* CoFHAPI|core (1.7.10R1.3.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHAPI|energy (1.7.10R1.3.1) from FloodLights-1.5.4.jar
		* CoFHAPI|fluid (1.7.10R1.3.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHAPI|inventory (1.7.10R1.3.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHAPI|item (1.7.10R1.0.13B1) from extrautilities-1.2.13a.jar
		* CoFHAPI|modhelpers (1.7.10R1.3.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHAPI|tileentity (1.7.10R1.3.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHAPI|transport (1.7.10R1.3.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHAPI|world (1.7.10R1.3.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|audio (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|gui (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|gui|container (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|gui|element (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|gui|element|listbox (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|gui|slot (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|inventory (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|render (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|render|particle (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|util (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|util|helpers (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|util|position (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|world (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* CoFHLib|world|feature (1.7.10R1.2.1) from CoFHCore-[1.7.10]3.1.4-329.jar
		* computronicsAPI (1.3) from Computronics-1.9.3-GTNH.jar
		* computronicsAPI|audio (1.0) from Computronics-1.9.3-GTNH.jar
		* computronicsAPI|chat (1.3) from Computronics-1.9.3-GTNH.jar
		* computronicsAPI|multiperipheral (1.1) from Computronics-1.9.3-GTNH.jar
		* computronicsAPI|tape (1.0) from Computronics-1.9.3-GTNH.jar
		* CraftingTweaks|API (4.1) from bogosorter-1.2.51-GTNH.jar
		* DraconicEvolution|API (1.2) from Draconic-Evolution-1.4.24-GTNH.jar
		* DualWield (0.1) from battlegear2-1.5.5-backhand.jar
		* EnderIOAPI (0.0.2) from EnderIO-2.9.22.jar
		* EnderIOAPI|Redstone (0.0.2) from EnderIO-2.9.22.jar
		* EnderIOAPI|Teleport (0.0.2) from EnderIO-2.9.22.jar
		* EnderIOAPI|Tools (0.0.2) from EnderIO-2.9.22.jar
		* ForestryAPI|apiculture (5.0.0) from Forestry-4.10.16.jar
		* ForestryAPI|arboriculture (4.2.1) from Forestry-4.10.16.jar
		* ForestryAPI|circuits (3.1.0) from Forestry-4.10.16.jar
		* ForestryAPI|core (5.0.0) from Forestry-4.10.16.jar
		* ForestryAPI|farming (2.1.0) from Forestry-4.10.16.jar
		* ForestryAPI|food (1.1.0) from Forestry-4.10.16.jar
		* ForestryAPI|fuels (2.0.1) from Forestry-4.10.16.jar
		* ForestryAPI|genetics (4.7.1) from Forestry-4.10.16.jar
		* ForestryAPI|hives (4.1.0) from Forestry-4.10.16.jar
		* ForestryAPI|lepidopterology (1.3.0) from Forestry-4.10.16.jar
		* ForestryAPI|mail (3.0.0) from Forestry-4.10.16.jar
		* ForestryAPI|multiblock (3.0.0) from Forestry-4.10.16.jar
		* ForestryAPI|recipes (5.4.0) from Forestry-4.10.16.jar
		* ForestryAPI|storage (3.0.0) from Forestry-4.10.16.jar
		* ForestryAPI|world (2.1.0) from Forestry-4.10.16.jar
		* ForgeRelocation|API (0.3.3) from ForgeRelocation-0.3.3.jar
		* Galacticraft API (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Blocks (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Client Events (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Core Prefabs (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Entities (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Entity Prefabs (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Galaxies (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Items (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Oxygen Events (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Power (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Prefabs (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Recipes (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Tile Entities (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Transmission (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Transmission|Grids (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Transmission|Tile Entities (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|Vectors (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|World (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|World Generation Events (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* Galacticraft API|World Generation Prefabs (3.3.10-GTNH) from Galacticraft-3.3.10-GTNH.jar
		* gendustryAPI (2.3.0) from gendustry-1.9.4-GTNH.jar
		* Heraldry (alpha) from battlegear2-1.5.5-backhand.jar
		* IC2API (1.0) from industrialcraft-2-2.2.828a-experimental.jar
		* MouseTweaks|API (1.0) from bogosorter-1.2.51-GTNH.jar
		* NuclearControlAPI (v1.0.5) from IC2NuclearControl-2.6.18.jar
		* OilTweakAPI (1.1.1) from BuildCraftOilTweak-1.1.1.jar
		* OilTweakAPI|blacklist (1.1.1) from BuildCraftOilTweak-1.1.1.jar
		* OpenBlocks|API (1.1) from OpenBlocks-1.11.7-GTNH.jar
		* OpenComputersAPI|Component (6.0.0-alpha) from OpenComputers-1.11.16-GTNH.jar
		* OpenComputersAPI|Core (6.0.0-alpha) from OpenComputers-1.11.16-GTNH.jar
		* OpenComputersAPI|Driver (6.0.0-alpha) from OpenComputers-1.11.16-GTNH.jar
		* OpenComputersAPI|Driver|Item (6.0.0-alpha) from OpenComputers-1.11.16-GTNH.jar
		* OpenComputersAPI|Event (6.0.0-alpha) from OpenComputers-1.11.16-GTNH.jar
		* OpenComputersAPI|FileSystem (6.0.0-alpha) from OpenComputers-1.11.16-GTNH.jar
		* OpenComputersAPI|Internal (6.0.0-alpha) from OpenComputers-1.11.16-GTNH.jar
		* OpenComputersAPI|Machine (6.0.0-alpha) from OpenComputers-1.11.16-GTNH.jar
		* OpenComputersAPI|Manual (6.0.0-alpha) from OpenComputers-1.11.16-GTNH.jar
		* OpenComputersAPI|Network (6.0.0-alpha) from OpenComputers-1.11.16-GTNH.jar
		* OpenComputersAPI|Prefab (6.0.0-alpha) from OpenComputers-1.11.16-GTNH.jar
		* ProjectRed API (4.11.8-GTNH) from ProjRed-4.11.8-GTNH.jar
		* Quiver (0.2) from battlegear2-1.5.5-backhand.jar
		* RailcraftAPI|bore (1.0.0) from Railcraft-9.16.32.jar
		* RailcraftAPI|carts (1.6.0) from Railcraft-9.16.32.jar
		* RailcraftAPI|core (1.5.0) from Railcraft-9.16.32.jar
		* RailcraftAPI|crafting (1.0.0) from Railcraft-9.16.32.jar
		* RailcraftAPI|electricity (2.0.0) from Railcraft-9.16.32.jar
		* RailcraftAPI|events (1.0.0) from Railcraft-9.16.32.jar
		* RailcraftAPI|fuel (1.0.0) from Railcraft-9.16.32.jar
		* RailcraftAPI|helpers (1.1.0) from Railcraft-9.16.32.jar
		* RailcraftAPI|items (1.0.0) from Railcraft-9.16.32.jar
		* RailcraftAPI|locomotive (1.1.0) from Railcraft-9.16.32.jar
		* RailcraftAPI|signals (2.3.0) from Railcraft-9.16.32.jar
		* RailcraftAPI|tracks (2.3.0) from Railcraft-9.16.32.jar
		* RemoteIO|API (2.7.6) from RemoteIO-2.7.6.jar
		* Shield (0.1) from battlegear2-1.5.5-backhand.jar
		* StorageDrawersAPI (1.7.10-1.2.0) from StorageDrawers-2.1.6-GTNH.jar
		* StorageDrawersAPI|config (1.7.10-1.2.0) from StorageDrawers-2.1.6-GTNH.jar
		* StorageDrawersAPI|event (1.7.10-1.2.0) from StorageDrawers-2.1.6-GTNH.jar
		* StorageDrawersAPI|inventory (1.7.10-1.2.0) from StorageDrawers-2.1.6-GTNH.jar
		* StorageDrawersAPI|pack (1.7.10-1.2.0) from StorageDrawers-2.1.6-GTNH.jar
		* StorageDrawersAPI|registry (1.7.10-1.2.0) from StorageDrawers-2.1.6-GTNH.jar
		* StorageDrawersAPI|render (1.7.10-1.2.0) from StorageDrawers-2.1.6-GTNH.jar
		* StorageDrawersAPI|storage (1.7.10-1.2.0) from StorageDrawers-2.1.6-GTNH.jar
		* StorageDrawersAPI|storage-attribute (1.7.10-1.2.0) from StorageDrawers-2.1.6-GTNH.jar
		* TC4Tweaks|API (1.5.32) from Thaumcraft4Tweaks-1.5.35.jar
		* Thaumcraft|API (4.2.2.0) from Thaumcraft-1.7.10-4.2.3.5a.jar
		* thaumicenergistics|API (1.1) from thaumicenergistics-1.7.11-GTNH.jar
		* WailaAPI (1.2) from Waila-1.8.12.jar
		* Weapons (0.1) from battlegear2-1.5.5-backhand.jar
	EnderIO: Found the following problem(s) with your installation:
                 An unsupported base software is installed: 'thermos, cauldron, craftbukkit, mcpc, kcauldron'. This is NOT supported.
                 This may have caused the error. Try reproducing the crash WITHOUT this/these mod(s) before reporting it.
	Chisel: Errors like "[FML]: Unable to lookup ..." are NOT the cause of this crash. You can safely ignore these errors. And update forge while you're at it.
	Forestry : Warning: You have mods that change the behavior of Minecraft, ForgeModLoader, and/or Minecraft Forge to your client: 
Bukkit, Cauldron, or other Bukkit replacement
These may have caused this error, and may not be supported. Try reproducing the crash WITHOUT these mods, and report it then.
Info: The following plugins have been disabled in the config: magicalcrops
	AE2 Integration: IC2:OFF, RotaryCraft:OFF, RC:OFF, BuildCraftCore:ON, BuildCraftTransport:ON, BuildCraftBuilder:ON, RF:ON, RFItem:ON, CoFHWrench:ON, MFR:OFF, DSU:ON, FZ:OFF, FMP:ON, RB:OFF, CLApi:OFF, Waila:ON, Mekanism:OFF, ImmibisMicroblocks:OFF, BetterStorage:OFF, OpenComputers:ON, PneumaticCraft:OFF, GT:ON, Chisel:ON, Jabba:ON, ThaumicTinkerer:ON
	Mixins in Stacktrace: 
		net.minecraft.server.MinecraftServer:
			mixins.visualprospecting.early.json:minecraft.MinecraftServerAccessor from mod visualprospecting
			mixins.serverutilities.early.json:minecraft.MixinMinecraftServer_PauseWhenEmpty from mod serverutilities
			mixins.gtnhlib.early.json:MixinMinecraftServer from mod gtnhlib
			mixins.serverutilities.early.json:minecraft.vanish.MixinMinecraftServer from mod serverutilities
			mixins.bugtorch.early.json:minecraft.fastrandom.MixinMinecraftServer from mod bugtorch
		net.minecraft.server.dedicated.DedicatedServer:
			mixins.serverutilities.early.json:minecraft.MixinDedicatedServer_PauseWhenEmpty from mod serverutilities
		cpw.mods.fml.common.LoadController:
			mixin.mixinbooterlegacy.json:LoadControllerMixin from mod (unknown)
			mixins.gtnhmixins.json:LateMixinOrchestrationMixin from mod (unknown)
	Profiler Position: N/A (disabled)
	Player Count: 0 / 20; []
	Is Modded: Definitely; Server brand changed to 'thermos,cauldron,craftbukkit,mcpc,kcauldron,fml,forge'
	Type: Dedicated Server (map_server.txt)