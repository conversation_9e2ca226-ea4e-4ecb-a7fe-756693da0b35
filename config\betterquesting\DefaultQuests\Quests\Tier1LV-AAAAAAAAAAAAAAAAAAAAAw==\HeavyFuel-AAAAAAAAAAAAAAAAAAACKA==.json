{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 550}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 551}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 543, "type:1": 1}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "By adding Hydrogen to sulfuric Heavy Fuel in a Chemical Reactor you can produce Heavy Fuel, with a burn value of 360,000 EU per cell! Aside from this, it's also required for Diesel, in MV, which is a better fuel, and it can also be distilled into three very useful fluids: Benzene, Toluene and Phenol.\n\nIf you want to use Heavy Fuel as a power source, you will need Semi-Fluid generators, unlike Light Fuel and Diesel which use Combustion generators.\n\nIt's honestly not especially efficient to make Heavy Fuel out of regular Oil. You should try to get Heavy Oil to make it, like from Oilsands Ore in MV once you can. Then use it to make Diesel.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 30741, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§5§lHeavy Fuel", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 552, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 8, "Damage:2": 30707, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 3, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinChemistI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 8, "Damage:2": 30741, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}}}