{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2611}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "The advanced machines you are now creating require more advanced materials, as well as more advanced ways to process them. In ZPM, you can construct the Hot Isostatic Pressurization Unit, an upgrade to the Large Electric Compressor. This multiblock heats material to extreme temperatures under extreme pressure, allowing it to create superdense plates with minimal imperfections.\n\nThe HIP Unit heats up while it is running, and must be given some time to cool off. If it reaches its maximum heat threshold, it will significantly slow your recipes or even void the inputs. Use better coils to increase how long it will take to overheat, and use Heat Sensor Hatches to automate it properly.\n\n[note]Can perform compression recipes which say \"Requires HIP Unit\" in NEI.[/note]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 3006, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Superdense", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": -5755629537772091783, "questIDLow:4": -7406677140276529890, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 3006, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings10"}, "1:10": {"Count:3": 1, "Damage:2": 5, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings10"}, "2:10": {"Count:3": 1, "Damage:2": 9, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings10"}, "3:10": {"Count:3": 1, "Damage:2": 10, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings10"}, "4:10": {"Count:3": 1, "Damage:2": 3009, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:optional_retrieval"}}}