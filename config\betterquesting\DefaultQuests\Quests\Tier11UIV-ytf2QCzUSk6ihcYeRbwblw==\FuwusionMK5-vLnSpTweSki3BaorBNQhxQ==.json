{"preRequisites:9": {"0:10": {"questIDHigh:4": 63121469939273035, "questIDLow:4": -6117290316329622558}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "After seeing how slow Shirabon is to make, you might want to upgrade your Fusion setup.\n\nLike its predecessor, the MK4, this version also supports Perfect Overclocks. Hopefully, this will make your Metastable Oganesson issues a bit more manageable.\n\n[note]You can specify the batch amount in the GUI if you don't want it to handle one-tick recipes.[/note]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 975, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Fuwusion MK5", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "OR", "visibility:8": "NORMAL"}}, "questIDHigh:4": -4847611916767573432, "questIDLow:4": -5258609888143859259, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 975, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 32023, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}}}