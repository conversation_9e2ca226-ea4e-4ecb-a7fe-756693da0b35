{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 850}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 851}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Bio diesel and diesel can be boosted using tetranitromethane. Ethenone and nitric acid are required to make tetranitromethane in a chemical reactor.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 30639, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§9§lTetranitromethane", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 841, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 8, "Damage:2": 30629, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 1, "Damage:2": 6, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "gregtech:gt.integrated_circuit"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinChemistI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 24, "Damage:2": 30653, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 3, "Damage:2": 30641, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 6, "Damage:2": 30639, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}}}