# Configuration file

"changing background" {
    S:alphaDecreaseStep=0.05

    # Whether backgrounds should change randomly during loading. They are taken from the random background list [default: true]
    B:backgroundChanging=true

    # Time in milliseconds between each image change (smooth blend). [range: 0.0 ~ 30000.0, default: 2000.0]
    S:blendTimeMilliseconds=2000.0

    # How many seconds between background changes [range: 1 ~ 9000, default: 40]
    I:changeFrequency=20

    # No, don't touch that! [default: false]
    B:shouldGLClear=false
    I:threadSleepTime=20
}


general {
    B:connectExternally=false

    # What font texture to use? Special Cases:
    #  - If you use the Russian mod "Client Fixer" then change this to "textures/font/ascii_fat.png"
    # Note: if a resourcepack adds a font, it will be used by B<PERSON>. [default: textures/font/ascii.png]
    S:font=textures/font/ascii.png

    # Play a sound after minecraft has finished starting up [default: true]
    B:playSound=true

    # What sound to play when loading is complete. Default is the level up sound (betterloadingscreen:rhapsodia_orb) [default: betterloadingscreen:rhapsodia_orb]
    S:sound=minecraft:random.orb

    # Whether or not to use minecraft's display to show the progress. This looks better, but there is a possibility of not being compatible, so if you do have any strange crash reports or compatibility issues, try setting this to false
    # Note: IIRC, setting this to false makes the screen black [default: true]
    B:useMinecraft=true
}


imgur {
    S:imgurGalleryLink=https://imgur.com/gallery/Ks0TrYE

    # Set to true if you want to load images from an imgur gallery and use them as backgrounds. [default: false]
    B:useImgur=false
}


layout {
    # Material loading bar position [default: [0, 0, 194, 24, 0, -83, 188, 12]]
    S:GTProgressBarPos=[0, 0, 194, 24, 0, -83, 188, 12]

    # Material animated loading bar position [default: [0, 24, 194, 24, 0, -83, 188, 12]]
    S:GTProgressBarPosAnimated=[0, 24, 194, 24, 0, -83, 188, 12]

    # Path to background resource.
    # You can use a resourcepack or resource loader for custom resources. [default: betterloadingscreen:textures/backgrounds/01.png]
    S:background=betterloadingscreen:textures/backgrounds/background1.png

    # List of paths to backgrounds that will be used if randomBackgrounds is true.
    # The paths must be separated by commas. [default: {betterloadingscreen:textures/backgrounds/01.png, betterloadingscreen:textures/backgrounds/02.png}]
    S:backgroundList={betterloadingscreen:textures/backgrounds/background1.png, betterloadingscreen:textures/backgrounds/background2.png, betterloadingscreen:textures/backgrounds/background3.png, betterloadingscreen:textures/backgrounds/background4.png, betterloadingscreen:textures/backgrounds/background5.png, betterloadingscreen:textures/backgrounds/background6.png, betterloadingscreen:textures/backgrounds/background7.png, betterloadingscreen:textures/backgrounds/background8.png, betterloadingscreen:textures/backgrounds/background9.png, betterloadingscreen:textures/backgrounds/background10.png, betterloadingscreen:textures/backgrounds/background11.png, betterloadingscreen:textures/backgrounds/background12.png, betterloadingscreen:textures/backgrounds/background14.png, betterloadingscreen:textures/backgrounds/background15.png, betterloadingscreen:textures/backgrounds/background16.png, betterloadingscreen:textures/backgrounds/background17.png, betterloadingscreen:textures/backgrounds/background18.png, betterloadingscreen:textures/backgrounds/background19.png, betterloadingscreen:textures/backgrounds/background20.png, betterloadingscreen:textures/backgrounds/background13.png, betterloadingscreen:textures/backgrounds/background21.png, betterloadingscreen:textures/backgrounds/background22.png, betterloadingscreen:textures/backgrounds/background23.png, betterloadingscreen:textures/backgrounds/background24.png, betterloadingscreen:textures/backgrounds/background25.png, betterloadingscreen:textures/backgrounds/background26.png, betterloadingscreen:textures/backgrounds/background27.png, betterloadingscreen:textures/backgrounds/background28.png, betterloadingscreen:textures/backgrounds/background29.png, betterloadingscreen:textures/backgrounds/background30.png, betterloadingscreen:textures/backgrounds/background31.png, betterloadingscreen:textures/backgrounds/background32.png, betterloadingscreen:textures/backgrounds/background33.png, betterloadingscreen:textures/backgrounds/background34.png, betterloadingscreen:textures/backgrounds/background35.png}

    # Transparency of main and GT material dynamic loading bar [range: 0.0 ~ 1.0, default: 0.5]
    S:loadingBarsAlpha=0.5

    # color of main and GT material dynamic loading bar (Use ffffff (white)) if you don't want to color it [default: fdf900]
    S:loadingBarsColor=fdf900

    # Path to main loading bar resource [default: betterloadingscreen:textures/mainProgressBar.png]
    S:mainProgressBar=betterloadingscreen:textures/mainProgressBar.png

    # Path to animated main loading bar resource [default: betterloadingscreen:textures/mainProgressBar.png]
    S:mainProgressBarAnimated=betterloadingscreen:textures/mainProgressBar.png

    # Main loading bar percentage position [default: [0, -40]]
    S:mainProgressBarPercentagePos=[0, -40]

    # Main loading bar position [default: [0, 0, 194, 24, 0, -50, 194, 16]]
    S:mainProgressBarPos=[0, 0, 194, 24, 0, -50, 194, 16]

    # Main animated loading bar position [default: [0, 24, 194, 24, 0, -50, 194, 16]]
    S:mainProgressBarPosAnimated=[0, 24, 194, 24, 0, -50, 194, 16]

    # Main loading bar text position. The four values are for position. [default: [0, -30]]
    S:mainProgressBarTextPos=[0, -30]

    # Path to materials loading bar [default: betterloadingscreen:textures/GTMaterialsprogressBars.png]
    S:materialProgressBar=betterloadingscreen:textures/GTMaterialsprogressBars.png

    # Path to animated materials loading bar [default: betterloadingscreen:textures/GTMaterialsprogressBars.png]
    S:materialProgressBarAnimated=betterloadingscreen:textures/GTMaterialsprogressBars.png

    # Material loading bar percentage position [default: [0, -75]]
    S:materialProgressBarPercentagePos=[0, -75]

    # Material loading bar text position. The two values are for position (x and y). [default: [0, -65]]
    S:materialProgressBarTextPos=[0, -65]

    # Memory bar position [default: [0, 0, 194, 24, 0, 48, 194, 16]]
    S:memoryBarPos=[0, 0, 194, 24, 0, 48, 194, 16]

    # Memory bar animated position [default: [0, 24, 194, 24, 0, 48, 194, 16]]
    S:memoryBarPosAnimated=[0, 24, 194, 24, 0, 48, 194, 16]

    # Whether display a random background from the random backgrounds list [default: true]
    B:randomBackgrounds=true

    # Color of text in hexadecimal format [default: ffffff]
    S:textColor=ffffff

    # Whether the text should be rendered with a shadow. Recommended, unless the background is really dark [default: true]
    B:textShadow=true

    # Path to logo/title resource [default: betterloadingscreen:textures/transparent.png]
    S:title=betterloadingscreen:textures/transparent.png

    # Logo coordinates in image and position.
    # the first four values indicate where the logo is located on the image (you could use a spritesheet),
    # the four next ones tell where the image will be located on screen like this:
    # [xLocation, yLocation, xWidth, yWidth, xLocation, yLocation, xWidth, yWidth]
    # The same is used for other images, except the background, which is fullscreen. Please ALWAYS provide
    # an image, a transparent one if you want even. BLS provides 'transparent.png' [default: [0, 0, 256, 256, 0, 50, 187, 145]]
    S:titlePos=[0, 0, 256, 256, 0, 50, 187, 145]
}


skepticism {
    # If you want to save a maximum of time on your loading time but don't want to face a black screen, try this. [default: false]
    B:salt=false
}


tips {
    # Base text position. Can be TOP_CENTER, TOP_RIGHT, CENTER_LEFT, CENTER, CENTER_RIGHT, BOTTOM_LEFT, BOTTOM_CENTER or BOTTOM_RIGHT.
    # Note: Other elements use CENTER, if you really need, ask to implement this base position option for any other element. [default: BOTTOM_CENTER]
    S:baseTipsTextPos=BOTTOM_CENTER

    # Custom tips file name, place it in config/Betterloadingscreen/tips. 
    # Don't include the ".txt". Example: "myTipFile" [default: en_US]
    S:customTipFilename=en_US

    # Time in seconds between tip changes [range: 1 ~ 9000, default: 18]
    I:tipsChangeFrequency=50

    # Set to true if you want to display random tips. Tips are stored in a separate file [default: true]
    B:tipsEnabled=true

    # Color of tips text in hexadecimal format [default: ffffff]
    S:tipsTextColor=ffffff

    # Tips text position [default: [0, 5]]
    S:tipsTextPos=[0, 10]

    # Whether the tips text should be rendered with a shadow. [default: true]
    B:tipsTextShadow=true

    # Set to true if you want a custom tips file/different locale than your Minecraft one. [default: false]
    B:useCustomTips=false
}


