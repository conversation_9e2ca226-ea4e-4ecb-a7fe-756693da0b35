{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1851}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "...But she'll never stop because you'll always have something to grind. You can use a hopper to keep the windmill full of items to process. Just remember to retrieve them from the dispensers.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BW_LeatherRotor"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§2§lAround and Around She Goes...", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1852, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 3, "Damage:2": 1, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}, "1:10": {"Count:3": 15, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinForestry"}, "2:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:hopper"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BW_CombinedRotor", "tag:10": {}}}, "taskID:8": "bq_standard:retrieval"}}}