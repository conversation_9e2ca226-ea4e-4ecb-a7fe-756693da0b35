{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 474}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Another copper source is \"malachite\" which can be found between Y 10-40. This vein contains more iron ore variants like \"brown limonite\", \"yellow limonite\" and \"banded iron ore\".\n\nTo see where an ore can be found, click the raw ore in NEI, and it will show you more details. Works with small ores too.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 871, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§2§lMalachite", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "OR", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 480, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 8, "Damage:2": 11035, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 1, "Damage:2": 32240, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.02"}, "2:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinAdventure"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 32, "Damage:2": 930, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}, "1:10": {"Count:3": 32, "Damage:2": 931, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}, "2:10": {"Count:3": 32, "Damage:2": 917, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}, "3:10": {"Count:3": 16, "Damage:2": 871, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 32, "Damage:2": 5930, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}, "1:10": {"Count:3": 32, "Damage:2": 5931, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}, "2:10": {"Count:3": 32, "Damage:2": 5917, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}, "3:10": {"Count:3": 16, "Damage:2": 5871, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}}, "taskID:8": "bq_standard:retrieval"}}}