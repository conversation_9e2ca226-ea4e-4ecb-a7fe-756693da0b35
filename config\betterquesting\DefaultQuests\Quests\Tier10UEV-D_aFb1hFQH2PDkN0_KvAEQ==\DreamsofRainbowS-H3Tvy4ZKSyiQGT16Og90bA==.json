{"preRequisites:9": {"0:10": {"questIDHigh:4": -139068609484536132, "questIDLow:4": -5753871526049634361}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "UEV energy hatches? Great! Time for stargate trophy number two.\nOnce again, to get your trophy you have to submit two stargates' worth of materials.\n\n§3Trophy number two brings us to chapter two of our journey through GT:NHs past, the early UEV era.\n\nThe year 2022 has just begun and the UEV tier, albeit still quite empty, has become more useful than before. UEV-UMV energy hatches now have proper recipes (although with UEV parts) and optical SMDs are a thing now, hooray.\nThere finally are better powergen options than regular plasma turbine spam, namely naq fuel and compact fusions + XL plasma turbine spam. Technically dyson too, but that is horribly bugged and its 'legit' power output reasonably can't exceed around 6 billion eu/t (around the same as a compact MK4 running helium plasma).\nSome useful newly introduced multis are the PrAss, MCR & Mega Oil Cracker and overclocks past MAX voltage are possible on megas now.\nStargate has once again become a distant goal, as it now required exorbitant amounts or infinity catalyst and bio circuits, requiring to utilize the new tech to its fullest.\n\nTwo players, §aPhineasor§3 & §aGDCloud§3 were determined to be first to completing this goal, and after months of a close friendly race, the winner had been decided. §a\nGDCloud§3 won the race and conquered this round of nerfs on the §a27th of May, 2022§3.\n\nP.S.: Once again, if you want to be faithful to the challenge, try completing it without these additions:\n-PCB Factory, Space Elevator, Component Assemblyline, AAL, EEC, EIG, MABS\n-Any bee related GT machines\n-Any UEV or above multis (except weak dyson and naq refineries)\n-LEG\n-Sub 1-tick OC, Batch Mode, inbuilt Void Protection on multis\n-Level Maintainer, AE Fluid Crafting, Stocking Buses/Hatches, Crafting Input Buses, ME Output Hatches\n\nP.P.S.: Again the chaotic capacitor banks have been left out for the same reason as stated in the UV gate quest. Oh and the stargate parts used to be UXV recipes, but since UMV hatches are not craftable in UEV anymore, they have been downtiered to UIV to make them craftable in UEV.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.MedalEngineer"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Dreams of Rainbow... Stargates?", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 2266700169884617512, "questIDLow:4": -8063346062660766612, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "minecraft:paper", "tag:10": {"display:10": {"Name:8": "Pick this item up to get your trophy"}}}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 1, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 625, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.PolychromeGatePlate"}, "1:10": {"Count:3": 342, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.PolychromeChevron"}, "2:10": {"Count:3": 1470, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.PolychromeFramePart"}, "3:10": {"Count:3": 82, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.PolychromeQuantumCircuit"}, "4:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.StargateCrystalAncients"}, "5:10": {"Count:3": 436, "Damage:2": 32649, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "6:10": {"Count:3": 138, "Damage:2": 32699, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "7:10": {"Count:3": 10, "Damage:2": 32689, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "8:10": {"Count:3": 736, "Damage:2": 32679, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "9:10": {"Count:3": 124, "Damage:2": 15, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings"}, "10:10": {"Count:3": 1590, "Damage:2": 13, "OreDict:8": "", "id:8": "gregtech:gt.blockmetal4"}, "11:10": {"Count:3": 1960, "Damage:2": 9, "OreDict:8": "", "id:8": "gregtech:gt.blockmetal7"}}, "taskID:8": "bq_standard:retrieval"}}}