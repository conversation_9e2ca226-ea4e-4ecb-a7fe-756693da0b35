{"preRequisites:9": {"0:10": {"questIDHigh:4": -6962518690474080185, "questIDLow:4": -6893323685044150631}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "With the project finally completed, and a new material capable of harnessing the power of a neutron star, you decide the natural first application of this immense power could be used for a far superior Blast Furnace. With a little tinkering, you decide a second may be necessary, since this module could easily be retrofitted into an ordinary Furnace as well.\n\n[note]The first module of the Godforge, Helioflare Power Forge can perform EBF recipes and Furnace recipes in separate running modes, toggleable in the UI. Note that the Furnace mode does NOT contribute towards the Conversion milestone![/note]\n\n[note]Usage of this module requires the START upgrade.[/note]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 15412, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§b§lAn Even More Mega EBF", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": -8986332492408667499, "questIDLow:4": -6067382707378224204, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 15412, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 20, "Damage:2": 0, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "1:10": {"Count:3": 20, "Damage:2": 2, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "2:10": {"Count:3": 5, "Damage:2": 12, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings5"}, "3:10": {"Count:3": 5, "Damage:2": 1, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "4:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}}, "taskID:8": "bq_standard:optional_retrieval"}}}