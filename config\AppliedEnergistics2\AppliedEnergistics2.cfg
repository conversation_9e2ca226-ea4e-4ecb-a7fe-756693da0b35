# Configuration file

automation {
    I:formationPlaneEntityLimit=128

    # #Network bytes information update Frequency(s) default:1.0
    D:networkBytesUpdateFrequency=1.0
}


battery {
    I:chargedStaff=8000
    I:colorApplicator=20000
    I:entropyManipulator=200000
    I:matterCannon=200000
    I:portableCell=200000
    I:wirelessTerminal=1600000
}


client {
    # Possible Values: NAME, AMOUNT, CRAFTS, MOD, PERCENT
    S:CRAFTING_SORT_BY=NAME

    # Possible Values: BUTTON, TILE
    S:CRAFTING_STATUS=TILE

    # Possible Values: YES, NO, UNDECIDED
    S:HIDE_STORED=NO

    # Possible Values: NATURAL, ALPHANUM
    S:INTERFACE_TERMINAL_SECTION_ORDER=NATURAL
    I:InterfaceTerminalSmallSize=6
    I:MEMonitorableSmallSize=6

    # Possible Values: DISABLED, ONE, TWO, THREE, FOUR
    S:PINS_STATE=DISABLED

    # Possible Values: AE, EU, WA, RF, MK
    S:PowerUnit=AE

    # Possible Values: YES, NO, UNDECIDED
    S:SAVE_SEARCH=NO

    # Possible Values: MANUAL_SEARCH, AUTOSEARCH, NEI_AUTOSEARCH, NEI_MANUAL_SEARCH
    S:SEARCH_MODE=AUTOSEARCH

    # Possible Values: YES, NO, UNDECIDED
    S:SEARCH_TOOLTIPS=YES

    # Possible Values: ASCENDING, DESCENDING
    S:SORT_DIRECTION=ASCENDING

    # Possible Values: SMALL, DYNAMIC, LARGE
    S:TERMINAL_FONT_SIZE=SMALL

    # Possible Values: TALL, FULL, SMALL
    S:TERMINAL_STYLE=TALL

    # Controls buttons on Crafting Screen : Capped at 9
    I:craftAmtButton1=1

    # Controls buttons on Crafting Screen : Capped at 99
    I:craftAmtButton2=10

    # Controls buttons on Crafting Screen : Capped at 999
    I:craftAmtButton3=100

    # Controls buttons on Crafting Screen : Capped at 9999
    I:craftAmtButton4=1000
    B:disableColoredCableRecipesInNEI=true
    B:enableEffects=true

    # Controls buttons on Level Emitter Screen : Capped at 9
    I:levelAmtButton1=1

    # Controls buttons on Level Emitter Screen : Capped at 99
    I:levelAmtButton2=10

    # Controls buttons on Level Emitter Screen : Capped at 999
    I:levelAmtButton3=100

    # Controls buttons on Level Emitter Screen : Capped at 9999
    I:levelAmtButton4=1000
    B:preserveSearchBar=true

    # Controls buttons on Priority Screen : Capped at 9
    I:priorityAmtButton1=1

    # Controls buttons on Priority Screen : Capped at 99
    I:priorityAmtButton2=10

    # Controls buttons on Priority Screen : Capped at 999
    I:priorityAmtButton3=100

    # Controls buttons on Priority Screen : Capped at 9999
    I:priorityAmtButton4=1000
    B:showOnlyInterfacesWithFreeSlotsInInterfaceTerminal=false
    B:useColoredCraftingStatus=true
    B:useTerminalUseLargeFont=false
}


condenser {
    I:MatterBalls=512
    I:Singularity=512000
}


craftingcpu {
    I:craftingCalculationTimePerTick=5
}


debug {
    B:CaptureGridAccessExceptionStacks=false
    I:CraftingCalculatorVersion=2
    B:LogPathFinding=false
    B:LogTiming=false
}


features {

    world {
        B:CertusQuartzWorldGen=false
        B:ChestLoot=true
        B:DecorativeLights=true
        B:DecorativeQuartzBlocks=true
        B:Flour=true
        B:GrindStone=false
        B:Inscriber=true
        B:MeteoriteWorldGen=true
        B:SkyStoneChests=true
        B:SpawnPressesInMeteorites=true
        B:TinyTNT=true
        B:VillagerTrading=true
    }

    toolsclassifications {
        B:CertusQuartzTools=true
        B:NetherQuartzTools=true
        B:PoweredTools=true
    }

    tools {
        B:ChargedStaff=true
        B:ColorApplicator=true
        B:EntropyManipulator=true
        B:MatterCannon=true
        B:MeteoriteCompass=true
        B:PaintBalls=true
        B:QuartzAxe=true
        B:QuartzHoe=true
        B:QuartzKnife=true
        B:QuartzPickaxe=true
        B:QuartzSpade=true
        B:QuartzSword=true
        B:QuartzWrench=true
        B:WirelessAccessTerminal=true
    }

    networkfeatures {
        B:Channels=true
        B:PowerGen=true
        B:QuantumNetworkBridge=true
        B:Security=true
        B:SpatialIO=true
    }

    networkbuses {
        B:AnnihilationPlane=true
        B:CraftingTerminal=true
        B:ExportBus=true
        B:FormationPlane=true
        B:IdentityAnnihilationPlane=true
        B:ImportBus=true
        B:LevelEmitter=true
        B:P2PTunnel=true
        B:PartConversionMonitor=true
        B:PartThroughputMonitor=true
        B:StorageBus=true
        B:StorageMonitor=true
    }

    portablecell {
        B:PortableCell=true
    }

    storage {
        B:IOPort=true
        B:MEChest=true
        B:MEDrive=true
        B:StorageCells=true
        B:XtremeStorageCells=true
    }

    networktool {
        B:NetworkTool=true
    }

    highercapacity {
        B:DenseCables=true
        B:DenseEnergyCells=true
    }

    p2ptunnels {
        B:P2PTunnelEU=true
        B:P2PTunnelGregtech=true
        B:P2PTunnelItems=true
        B:P2PTunnelLight=true
        B:P2PTunnelLiquids=true
        B:P2PTunnelME=true
        B:P2PTunnelOpenComputers=true
        B:P2PTunnelPressure=true
        B:P2PTunnelRF=true
        B:P2PTunnelRedstone=true
        B:P2PTunnelSound=true
    }

    blockfeatures {
        B:MassCannonBlockDamage=true
        B:TinyTNTBlockDamage=true
    }

    facades {
        B:Facades=true
    }

    misc {
        B:Achievements=true
        B:ComplexPatternLog=false
        B:CraftingLog=false
        B:Creative=true
        B:DebugLogging=false
        B:GrinderLogging=false
        B:IntegrationLogging=false
        B:LightDetector=true
        B:LogSecurityAudits=false
        B:Logging=true
        B:PacketLogging=false
        B:UnsupportedDeveloperTools=false
        B:UpdateLogging=false
        B:WebsiteRecipes=false
    }

    crafting {
        B:CustomRecipes=false
        B:EnableDisassemblyCrafting=true
        B:EnableFacadeCrafting=true
        B:InWorldFluix=true
        B:InWorldPurification=true
        B:InWorldSingularity=true
        B:InterfaceTerminal=true
    }

    rendering {
        B:AlphaPass=true
    }

    craftingfeatures {
        B:CraftingCPU=true
        B:MolecularAssembler=true
        B:Patterns=true
        B:PatternsOptimizer=true
        B:XtremeCraftingCPU=true
    }

    commands {
        B:ChunkLoggerTrace=false
    }

    advancednetworktool {
        B:AdvancedNetworkTool=true
    }

}


grindstone {
    S:grinderOres <
     >
    D:oreDoublePercentage=0.0
}


misc {
    B:LimitCraftingCPUSpill=true
    I:MaxCraftingSteps=2000000
    I:MaxCraftingTreeVisualizationSize=33554432
}


##########################################################################################################
# modintegration
#--------------------------------------------------------------------------------------------------------#
# Valid Values are 'AUTO', 'ON', or 'OFF' - defaults to 'AUTO' ; Suggested that you leave this alone unless your experiencing an issue, or wish to disable the integration for a reason.
##########################################################################################################

modintegration {
    S:BetterStorage=AUTO
    S:BuildCraftBuilders=AUTO
    S:BuildCraftCore=AUTO
    S:BuildCraftTransport=AUTO
    S:Chisel=AUTO
    S:CoFHWrench=AUTO
    S:ColoredLightsCore=AUTO
    S:CraftGuide=AUTO
    S:DeepStorageUnit=AUTO
    S:Factorization=AUTO
    S:ForgeMultiPart=AUTO
    S:GregTech=AUTO
    S:ImmibisMicroblocks=AUTO
    S:IndustrialCraft2=OFF
    S:InventoryTweaks=AUTO
    S:Jabba=AUTO
    S:Mekanism=AUTO
    S:MineFactoryReloaded=AUTO
    S:NotEnoughItems=AUTO
    S:OpenComputers=AUTO
    S:PneumaticCraft=AUTO
    S:Railcraft=OFF
    S:RedstoneFluxPower-Items=AUTO
    S:RedstoneFluxPower-Tiles=AUTO
    S:RotaryCraft=AUTO
    S:RotatableBlocks=AUTO
    S:ThaumicTinkerer=AUTO
    S:Waila=AUTO
}


##########################################################################################################
# orecamouflage
#--------------------------------------------------------------------------------------------------------#
# AE2 Automatically uses alternative ores present in your instance of MC to blend better with its surroundings, if you prefer you can disable this selectively using these flags; Its important to note, that some if these items even if enabled may not be craftable in game because other items are overriding their recipes.
##########################################################################################################

orecamouflage {
    # OreDictionary Names: crystalCertusQuartz
    B:CertusQuartzCrystal=true

    # OreDictionary Names: dustCertusQuartz
    B:CertusQuartzDust=true

    # OreDictionary Names: dustEnder,dustEnderPearl
    B:EnderDust=true

    # OreDictionary Names: dustWheat
    B:Flour=true

    # OreDictionary Names: dustGold
    B:GoldDust=true

    # OreDictionary Names: dustIron
    B:IronDust=true

    # OreDictionary Names: nuggetIron
    B:IronNugget=true

    # OreDictionary Names: dustNetherQuartz
    B:NetherQuartzDust=true

    # OreDictionary Names: itemSilicon
    B:Silicon=true
    B:WoodenGear=true
}


powerratios {
    D:IC2=2.0
    D:Mekanism=0.2
    D:RotaryCraft=8.884150675195451E-5
    D:ThermalExpansion=0.5
    D:TunnelPowerLoss=0.05
    D:UsageMultiplier=10.0
}


spatialio {
    D:spatialPowerExponent=1.35
    D:spatialPowerMultiplier=1250.0
    I:storageBiomeID=135
    I:storageProviderID=-11
}


##########################################################################################################
# tickrates
#--------------------------------------------------------------------------------------------------------#
#  Min / Max Tickrates for dynamic ticking, most of these components also use sleeping, to prevent constant ticking, adjust with care, non standard rates are not supported or tested.
##########################################################################################################

tickrates {
    I:AnnihilationPlane.max=120
    I:AnnihilationPlane.min=2
    I:ExportBus.max=60
    I:ExportBus.min=5
    I:IOPort.max=5
    I:IOPort.min=1
    I:ImportBus.max=40
    I:ImportBus.min=5
    I:Inscriber.max=1
    I:Inscriber.min=1
    I:Interface.max=120
    I:Interface.min=5
    I:ItemTunnel.max=60
    I:ItemTunnel.min=5
    I:LevelEmitterDelay=40
    I:LightTunnel.max=120
    I:LightTunnel.min=5
    I:METunnel.max=20
    I:METunnel.min=5
    I:OpenComputersTunnel.max=5
    I:OpenComputersTunnel.min=1
    I:PressureTunnel.max=120
    I:PressureTunnel.min=1
    I:SoundTunnel.max=120
    I:SoundTunnel.min=20
    I:StorageBus.max=60
    I:StorageBus.min=5
    I:ThroughputMonitor.max=100
    I:ThroughputMonitor.min=20
    I:VibrationChamber.max=40
    I:VibrationChamber.min=10
}


##########################################################################################################
# wireless
#--------------------------------------------------------------------------------------------------------#
# Range= WirelessBaseRange + WirelessBoosterRangeMultiplier * Math.pow( boosters, WirelessBoosterExp )
# PowerDrain= WirelessBaseCost + WirelessCostMultiplier * Math.pow( boosters, 1 + boosters / WirelessHighWirelessCount )
##########################################################################################################

wireless {
    D:WirelessBaseCost=8.0
    D:WirelessBaseRange=16.0
    D:WirelessBoosterExp=1.5
    D:WirelessBoosterRangeMultiplier=1.0
    D:WirelessConnectorPowerBase=1.0
    D:WirelessConnectorPowerDistanceMultiplier=0.1
    D:WirelessCostMultiplier=1.0
    D:WirelessTerminalDrainMultiplier=1.0
}


##########################################################################################################
# worldgen
#--------------------------------------------------------------------------------------------------------#
# The meteorite dimension whitelist list can be used alone or in unison with the meteorite (in)valid blocks whitelist. 
# Default debris is the following (in this order) Stone, Cobblestone, biomeFillerBlock (what's under the top block, usually dirt), Gravel, biomeTopBlock (usually grass) 
# Format: dimensionID, modID:blockID:metadata, modID:blockID:metadata, modID:blockID:metadata, modID:blockID:metadata, modID:blockID:metadata 
# --------------------------------------------------------------------------------------------------------# 
# The meteorite (in)valid spawn blocks list can be used alone or in unison with the meteorite dimension whitelist. Format: modId:blockID, modId:blockID, etc. 
##########################################################################################################

worldgen {
    D:meteoriteClusterChance=0.1
    S:meteoriteDimensionWhitelist <
        0, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT
     >
    S:meteoriteInvalidSpawnBlocks <
        examplemod:example_block
     >
    S:meteoriteSpawnChance <
        0=0.3
     >
    S:meteoriteValidSpawnBlocks <
        examplemod:example_block
     >
    S:minMeteoriteDistance <
        0=707
     >
    I:quartzOresClusterAmount=15
    I:quartzOresPerCluster=4
    D:spawnChargedChance=0.07999998331069946
}


