{"preRequisites:9": {"0:10": {"questIDHigh:4": -9177469786095531506, "questIDLow:4": -4804005527088963644}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Directly dissolve and chlorinate of lanthanide ores in Degister to get the concentrated solution of the corresponding ore. Then use the corresponding Extracting Nano Resin to extract the main material in the solution. The rest (Chlorinated Rare Earth Concentrate) is still rich in useful lanthanides.\n\nYou can use any type Extracting Nano Resin to extract the useful things in the rest. What Nano Resin you use, what lanthanide you'll get. Every bucket of Chlorinated Rare Earth Concentrate can do this extraction 3 times. First Concentrate to Enriched Solution, then Enriched Solution to Diluted Solution, then waste liquid.\n\nAfter extracting, the Nano Resin will be \"Filled\". Put them \"Filled\" into Electrolyzor then nanites will transfer the \"Particles\" out from the resin under the influence of electric field. And you can get the Nano Resins and let them go into the next work round, again and again without breaks.\n\nYes, Rare Earth will not be Rare. Bless the Nano Tech.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 78, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.99"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Extract what you want", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 3400405695913021327, "questIDLow:4": -4661739323824919812, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 11402, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "1:10": {"Count:3": 1, "Damage:2": 11404, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "2:10": {"Count:3": 1, "Damage:2": 11406, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "3:10": {"Count:3": 1, "Damage:2": 11408, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "4:10": {"Count:3": 1, "Damage:2": 11410, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "5:10": {"Count:3": 1, "Damage:2": 11413, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "6:10": {"Count:3": 1, "Damage:2": 11412, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "7:10": {"Count:3": 1, "Damage:2": 11414, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "8:10": {"Count:3": 1, "Damage:2": 11416, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "9:10": {"Count:3": 1, "Damage:2": 11418, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "10:10": {"Count:3": 1, "Damage:2": 11420, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "11:10": {"Count:3": 1, "Damage:2": 11422, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "12:10": {"Count:3": 1, "Damage:2": 11424, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "13:10": {"Count:3": 1, "Damage:2": 11426, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "14:10": {"Count:3": 1, "Damage:2": 11428, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "15:10": {"Count:3": 1, "Damage:2": 11430, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"index:3": 1, "taskID:8": "bq_standard:checkbox"}}}