# Configuration file

cache {
    S:digest=5ad1508224217cc8e338d20e6cbbcaed

    # Caching can save processing time, if there are a lot of items. [default: true]
    B:enableCache=true
}


general {
    # Will output more detailed information into the CSV like corresponding items [default: false]
    B:enableAdditionalInfo=false

    # If true, the CSV exporting will always happen. This will not use the cache to reduce the computation. [default: false]
    B:enableForceRefresh=false

    # If true, the custom recipes are enabled. Acts as a master switch. [default: true]
    B:enabled=true

    # If true, all registered items will be exported containing the internal minecraft name and the localized name to actually find the item you are using. This also contains the item representation of the blocks, but are missing items, which are too much to display e.g. FMP. [default: true]
    B:exportItemNames=true
}


