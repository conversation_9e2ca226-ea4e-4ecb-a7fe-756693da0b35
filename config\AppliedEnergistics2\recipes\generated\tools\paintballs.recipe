shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeWhite ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.White

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeBlack ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Black

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeRed ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Red

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeGreen ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Green

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeBrown ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Brown

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeBlue ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Blue

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyePurple ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Purple

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeCyan ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Cyan

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeLightGray ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.LightGray

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeGray ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Gray

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyePink ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Pink

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeLime ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Lime

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeYellow ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Yellow

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeLightBlue ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.LightBlue

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeMagenta ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Magenta

shaped=
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall oredictionary:dyeOrange ae2:ItemMaterial.MatterBall,
    ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall ae2:ItemMaterial.MatterBall
    -> 8 ae2:PaintBall.Orange

shaped=
    ae2:PaintBall.White ae2:PaintBall.White ae2:PaintBall.White,
    ae2:PaintBall.White oredictionary:dustGlowstone ae2:PaintBall.White,
    ae2:PaintBall.White ae2:PaintBall.White ae2:PaintBall.White
    -> 8 ae2:LumenPaintBall.White

shaped=
    ae2:PaintBall.Black ae2:PaintBall.Black ae2:PaintBall.Black,
    ae2:PaintBall.Black oredictionary:dustGlowstone ae2:PaintBall.Black,
    ae2:PaintBall.Black ae2:PaintBall.Black ae2:PaintBall.Black
    -> 8 ae2:LumenPaintBall.Black

shaped=
    ae2:PaintBall.Red ae2:PaintBall.Red ae2:PaintBall.Red,
    ae2:PaintBall.Red oredictionary:dustGlowstone ae2:PaintBall.Red,
    ae2:PaintBall.Red ae2:PaintBall.Red ae2:PaintBall.Red
    -> 8 ae2:LumenPaintBall.Red

shaped=
    ae2:PaintBall.Green ae2:PaintBall.Green ae2:PaintBall.Green,
    ae2:PaintBall.Green oredictionary:dustGlowstone ae2:PaintBall.Green,
    ae2:PaintBall.Green ae2:PaintBall.Green ae2:PaintBall.Green
    -> 8 ae2:LumenPaintBall.Green

shaped=
    ae2:PaintBall.Brown ae2:PaintBall.Brown ae2:PaintBall.Brown,
    ae2:PaintBall.Brown oredictionary:dustGlowstone ae2:PaintBall.Brown,
    ae2:PaintBall.Brown ae2:PaintBall.Brown ae2:PaintBall.Brown
    -> 8 ae2:LumenPaintBall.Brown

shaped=
    ae2:PaintBall.Blue ae2:PaintBall.Blue ae2:PaintBall.Blue,
    ae2:PaintBall.Blue oredictionary:dustGlowstone ae2:PaintBall.Blue,
    ae2:PaintBall.Blue ae2:PaintBall.Blue ae2:PaintBall.Blue
    -> 8 ae2:LumenPaintBall.Blue

shaped=
    ae2:PaintBall.Purple ae2:PaintBall.Purple ae2:PaintBall.Purple,
    ae2:PaintBall.Purple oredictionary:dustGlowstone ae2:PaintBall.Purple,
    ae2:PaintBall.Purple ae2:PaintBall.Purple ae2:PaintBall.Purple
    -> 8 ae2:LumenPaintBall.Purple

shaped=
    ae2:PaintBall.Cyan ae2:PaintBall.Cyan ae2:PaintBall.Cyan,
    ae2:PaintBall.Cyan oredictionary:dustGlowstone ae2:PaintBall.Cyan,
    ae2:PaintBall.Cyan ae2:PaintBall.Cyan ae2:PaintBall.Cyan
    -> 8 ae2:LumenPaintBall.Cyan

shaped=
    ae2:PaintBall.LightGray ae2:PaintBall.LightGray ae2:PaintBall.LightGray,
    ae2:PaintBall.LightGray oredictionary:dustGlowstone ae2:PaintBall.LightGray,
    ae2:PaintBall.LightGray ae2:PaintBall.LightGray ae2:PaintBall.LightGray
    -> 8 ae2:LumenPaintBall.LightGray

shaped=
    ae2:PaintBall.Gray ae2:PaintBall.Gray ae2:PaintBall.Gray,
    ae2:PaintBall.Gray oredictionary:dustGlowstone ae2:PaintBall.Gray,
    ae2:PaintBall.Gray ae2:PaintBall.Gray ae2:PaintBall.Gray
    -> 8 ae2:LumenPaintBall.Gray

shaped=
    ae2:PaintBall.Pink ae2:PaintBall.Pink ae2:PaintBall.Pink,
    ae2:PaintBall.Pink oredictionary:dustGlowstone ae2:PaintBall.Pink,
    ae2:PaintBall.Pink ae2:PaintBall.Pink ae2:PaintBall.Pink
    -> 8 ae2:LumenPaintBall.Pink

shaped=
    ae2:PaintBall.Lime ae2:PaintBall.Lime ae2:PaintBall.Lime,
    ae2:PaintBall.Lime oredictionary:dustGlowstone ae2:PaintBall.Lime,
    ae2:PaintBall.Lime ae2:PaintBall.Lime ae2:PaintBall.Lime
    -> 8 ae2:LumenPaintBall.Lime

shaped=
    ae2:PaintBall.Yellow ae2:PaintBall.Yellow ae2:PaintBall.Yellow,
    ae2:PaintBall.Yellow oredictionary:dustGlowstone ae2:PaintBall.Yellow,
    ae2:PaintBall.Yellow ae2:PaintBall.Yellow ae2:PaintBall.Yellow
    -> 8 ae2:LumenPaintBall.Yellow

shaped=
    ae2:PaintBall.LightBlue ae2:PaintBall.LightBlue ae2:PaintBall.LightBlue,
    ae2:PaintBall.LightBlue oredictionary:dustGlowstone ae2:PaintBall.LightBlue,
    ae2:PaintBall.LightBlue ae2:PaintBall.LightBlue ae2:PaintBall.LightBlue
    -> 8 ae2:LumenPaintBall.LightBlue

shaped=
    ae2:PaintBall.Magenta ae2:PaintBall.Magenta ae2:PaintBall.Magenta,
    ae2:PaintBall.Magenta oredictionary:dustGlowstone ae2:PaintBall.Magenta,
    ae2:PaintBall.Magenta ae2:PaintBall.Magenta ae2:PaintBall.Magenta
    -> 8 ae2:LumenPaintBall.Magenta

shaped=
    ae2:PaintBall.Orange ae2:PaintBall.Orange ae2:PaintBall.Orange,
    ae2:PaintBall.Orange oredictionary:dustGlowstone ae2:PaintBall.Orange,
    ae2:PaintBall.Orange ae2:PaintBall.Orange ae2:PaintBall.Orange
    -> 8 ae2:LumenPaintBall.Orange
