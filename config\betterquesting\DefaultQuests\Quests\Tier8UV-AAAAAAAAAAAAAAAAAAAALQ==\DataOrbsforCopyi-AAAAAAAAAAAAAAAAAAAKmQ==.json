{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2712}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "After getting your sample you need to encode the information to a Data Orb. Again, many times you'll have to do it repeatedly (the Data Orb is given back if it fails), but in this case it's 100%.\n\n[note]Look at the uses of the eColi DNA Sample to see the recipe.[/note]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "bartworks:BioLabModules"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Data Orbs for Copying Cultures", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2713, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 32707, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01", "tag:10": {"mDataName:8": "<PERSON>scher<PERSON><PERSON> koli", "mDataTitle:8": "DNA Sample"}}}, "taskID:8": "bq_standard:retrieval"}}}