shaped=
    glass glass glass,
    netherCrystal netherCrystal netherCrystal,
    oredictionary:slabWood oredictionary:slabWood oredictionary:slabWood
    -> mc:daylight_detector

shaped=
    _ mc:redstone_torch _,
    mc:redstone_torch netherCrystal mc:redstone_torch,
    oredictionary:stone oredictionary:stone oredictionary:stone
    -> mc:comparator
    
shaped=
    ae2:ItemMaterial.IronNugget ae2:ItemMaterial.IronNugget ae2:ItemMaterial.IronNugget,
    ae2:ItemMaterial.IronNugget ae2:ItemMaterial.IronNugget ae2:ItemMaterial.IronNugget,
    ae2:ItemMaterial.IronNugget ae2:ItemMaterial.IronNugget ae2:ItemMaterial.IronNugget
    -> mc:iron_ingot

shapeless=
    oredictionary:ingotIron
    -> 9 ae2:ItemMaterial.IronNugget
