{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 496}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "There are a few very useful Nether trees out there which give resources like Redstone, Gunpowder, Bonemeal or Fertilizer.\n\nGo and get a few trees like Bloodwood, Fusewood, Darkwood or Ghostwood out of the Nether.\n\nThese trees can be planted like normal trees, except for Bloodwood. That has to be planted on a ceiling and will grow down instead of up.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "Natura:Natura.netherfood"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§3§lNice Nether Trees", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1741, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag", "tag:10": {"ench:9": {"0:10": {"id:2": 35, "lvl:2": 3}}}}, "1:10": {"Count:3": 4, "Damage:2": 6, "OreDict:8": "", "id:8": "EnderIO:itemAlloy"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}, "1:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinAdventure"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 16, "Damage:2": 1, "OreDict:8": "", "id:8": "Natura:floraleavesnocolor"}, "1:10": {"Count:3": 4, "Damage:2": 4, "OreDict:8": "", "id:8": "Natura:flora<PERSON><PERSON>"}, "2:10": {"Count:3": 8, "Damage:2": 2, "OreDict:8": "", "id:8": "Natura:tree"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 16, "Damage:2": 2, "OreDict:8": "", "id:8": "Natura:floraleavesnocolor"}, "1:10": {"Count:3": 8, "Damage:2": 0, "OreDict:8": "", "id:8": "Natura:bloodwood"}, "2:10": {"Count:3": 8, "Damage:2": 15, "OreDict:8": "", "id:8": "Natura:bloodwood"}, "3:10": {"Count:3": 4, "Damage:2": 5, "OreDict:8": "", "id:8": "Natura:flora<PERSON><PERSON>"}}, "taskID:8": "bq_standard:retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 8, "Damage:2": 0, "OreDict:8": "", "id:8": "Natura:Dark Tree"}, "1:10": {"Count:3": 4, "Damage:2": 6, "OreDict:8": "", "id:8": "Natura:flora<PERSON><PERSON>"}, "2:10": {"Count:3": 16, "Damage:2": 0, "OreDict:8": "", "id:8": "Natura:Dark Leaves"}, "3:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "Natura:Natura.netherfood"}}, "taskID:8": "bq_standard:retrieval"}, "3:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 3, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 8, "Damage:2": 1, "OreDict:8": "", "id:8": "Natura:Dark Tree"}, "1:10": {"Count:3": 16, "Damage:2": 3, "OreDict:8": "", "id:8": "Natura:Dark Leaves"}, "2:10": {"Count:3": 4, "Damage:2": 7, "OreDict:8": "", "id:8": "Natura:flora<PERSON><PERSON>"}}, "taskID:8": "bq_standard:retrieval"}}}