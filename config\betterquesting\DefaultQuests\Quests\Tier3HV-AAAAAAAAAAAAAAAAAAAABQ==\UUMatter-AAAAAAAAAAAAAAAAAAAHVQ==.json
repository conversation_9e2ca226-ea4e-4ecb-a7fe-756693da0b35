{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1876}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Now you can actually make the UUM with a Mass Fabricator. Well, technically you could make it without UUA, but that's way more expensive. To make sure it only runs with UUA, put a programmed circuit (any value) in the machine's slot. UUMatter can be used for some very specific things, such as replicating §osome§r materials (check NEI, mostly/all periodic table), used with crops to create an ore, some specific high-level recipes need it, and...you can also sit in it to get regen.\n\nThe energy usage and overclocking of the Mass Fabricator is different from the usual GT way. To have fixed EU to UUM ratios for all tiers, the machine does perfect 2/2 overclocks. It also starts with a very high amperage in LV. The following chart should make it more clear:\n\nTier  Amps       EU/t         Time\nLV     8A         256          160\nMV     4A         512           80\nHV     2A        1024           40\nEV     1A        2048           20\nIV     0.5A      4096           10\netc.\n\nPut in power (and hopefully UUA), and out comes UUM.\n\nFor now, just stockpile the UU matter. You need data orbs to use the replicator, and that's explained next tier.\n\n[note]You only need to complete one of the tasks.[/note]", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 461, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§9§lUUMatter", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "OR", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1877, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 30721, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 16, "Damage:2": 11306, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:blockTank"}, "3:10": {"Count:3": 1, "Damage:2": 6, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 2, "Damage:2": 461, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 462, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}}}