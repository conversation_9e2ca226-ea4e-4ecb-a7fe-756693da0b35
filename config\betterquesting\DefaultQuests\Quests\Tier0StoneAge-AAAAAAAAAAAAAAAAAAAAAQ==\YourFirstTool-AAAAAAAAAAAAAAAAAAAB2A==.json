{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 33}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "When your tinkers tables are ready, you can craft your first §oreal§r tool. If you didn't find a village yet to grab some parts from the chest, you can only craft flint tools.\n\nLet's start with a pickaxe, an axe, and a shovel.\n\nKeep in mind that most of the original tool parts from Tinker's Construct are not shown in NEI. That doesn't mean that you can't craft them - use the tooltips on patterns and molds as guidance.\n\nYour new tools are weak at first, but the more you use them the better they will get. You get a modifier slot after some levels, needing more as you level it up. They will level up to level 99 if you play long enough, with 20 modifier slots. Tool parts can be replaced for a bit of level XP, however the tool must be fully repaired first.\n\nYou need to level up the mining level each time you replace the parts to unleash the full power of the pickaxe. Stone->Copper for this pickaxe. If you manage to get a Zombie, Skeleton, or Creeper head, you can use it to skip the level-up process. This doesn't use up a modifier level, but does change the look until you change the pickaxe part out.\n\nItem=Usable when mining level is X or less\nZombie=1\nSkeleton=2\nCreeper=4\nWither Skeleton=7\nNether Star=8\n\nYou could use a paper binding to add an extra modifier, but you will also get modifiers with tool levels.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:pickaxe", "tag:10": {"InfiTool:10": {"Accessory:4": 3, "Attack:4": 3, "BaseAttack:4": 3, "BaseDurability:4": 56, "BonusDurability:4": 0, "Broken:4": 0, "Built:4": 1, "Damage:4": 0, "Handle:4": 3, "HarvestLevel:4": 0, "HarvestLevelModified:4": 0, "Head:4": 3, "HeadEXP:4": 0, "MiningSpeed:4": 400, "ModDurability:6": 0.0, "Modifiers:4": 0, "RenderAccessory:4": 3, "RenderHandle:4": 3, "RenderHead:4": 3, "Shoddy:6": 0.0, "ToolEXP:4": 0, "ToolLevel:4": 1, "TotalDurability:4": 56, "Unbreaking:4": 0}, "display:10": {"Name:8": "Flint Pickaxe"}}}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§2§lYour First Tool", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 472, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 10, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:flint"}, "1:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:blankPattern"}}}, "1:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}, "1:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:blankPattern"}}, "ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:choice"}, "2:10": {"ignoreDisabled:1": 0, "index:3": 2, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 10, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSmith"}, "1:10": {"Count:3": 10, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:blankPattern"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "TConstruct:woodPattern"}, "1:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "TConstruct:woodPattern"}, "2:10": {"Count:3": 1, "Damage:2": 9, "OreDict:8": "", "id:8": "TConstruct:woodPattern"}, "3:10": {"Count:3": 1, "Damage:2": 3, "OreDict:8": "", "id:8": "TConstruct:woodPattern"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 3, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:flint"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "plankWood", "id:8": "minecraft:planks"}}, "taskID:8": "bq_standard:optional_retrieval"}, "3:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 3, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 3, "OreDict:8": "", "id:8": "TConstruct:pickaxeHead"}, "1:10": {"Count:3": 1, "Damage:2": 3, "OreDict:8": "", "id:8": "TConstruct:shovelHead"}, "2:10": {"Count:3": 1, "Damage:2": 3, "OreDict:8": "", "id:8": "TConstruct:hatchetHead"}, "3:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:binding"}, "4:10": {"Count:3": 3, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:stick"}}, "taskID:8": "bq_standard:retrieval"}, "4:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 4, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:pickaxe", "tag:10": {"InfiTool:10": {"Accessory:4": 0, "Attack:4": 3, "BaseAttack:4": 3, "BaseDurability:4": 113, "BonusDurability:4": 0, "Broken:4": 0, "Damage:4": 0, "Handle:4": 0, "HarvestLevel:4": 0, "HarvestLevelModified:4": 0, "Head:4": 3, "HeadEXP:4": 0, "MiningSpeed:4": 400, "ModDurability:6": 0.0, "Modifiers:4": 0, "RenderAccessory:4": 0, "RenderHandle:4": 0, "RenderHead:4": 3, "Shoddy:6": 0.0, "ToolEXP:4": 0, "ToolLevel:4": 1, "TotalDurability:4": 113, "Unbreaking:4": 0}, "display:10": {"Name:8": "Flint Pickaxe"}}}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:hatchet", "tag:10": {"InfiTool:10": {"Attack:4": 5, "BaseAttack:4": 5, "BaseDurability:4": 113, "BonusDurability:4": 0, "Broken:4": 0, "Damage:4": 0, "Handle:4": 0, "HarvestLevel:4": 1, "Head:4": 3, "MiningSpeed:4": 400, "ModDurability:6": 0.0, "Modifiers:4": 0, "RenderHandle:4": 0, "RenderHead:4": 3, "Shoddy:6": 0.0, "ToolEXP:4": 0, "ToolLevel:4": 1, "TotalDurability:4": 113, "Unbreaking:4": 0}, "display:10": {"Name:8": "Flint Hatchet"}}}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:shovel", "tag:10": {"InfiTool:10": {"Attack:4": 4, "BaseAttack:4": 4, "BaseDurability:4": 113, "BonusDurability:4": 0, "Broken:4": 0, "Damage:4": 0, "Handle:4": 0, "HarvestLevel:4": 1, "Head:4": 3, "MiningSpeed:4": 400, "ModDurability:6": 0.0, "Modifiers:4": 0, "RenderHandle:4": 0, "RenderHead:4": 3, "Shoddy:6": 0.0, "ToolEXP:4": 0, "ToolLevel:4": 1, "TotalDurability:4": 113, "Unbreaking:4": 0}, "display:10": {"Name:8": "<PERSON>"}}}}, "taskID:8": "bq_standard:retrieval"}}}