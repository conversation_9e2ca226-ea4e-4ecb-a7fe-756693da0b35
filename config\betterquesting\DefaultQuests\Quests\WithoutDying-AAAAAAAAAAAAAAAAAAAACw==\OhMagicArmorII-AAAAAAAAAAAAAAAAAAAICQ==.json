{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1159}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 1807}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Steeleaf armor is the next level in Twilight Forest armor. It can be handy as a defense in the Nether.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.steeleafPlate", "tag:10": {"ench:9": {"0:10": {"id:4": 3, "lvl:4": 2}}}}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "\"Oh, Magic\" Armor II", "partySingleReward:1": 0, "questLogic:8": "OR", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2057, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.steeleafAxe", "tag:10": {"ench:9": {"0:10": {"id:4": 32, "lvl:4": 2}}}}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.steeleafPick", "tag:10": {"ench:9": {"0:10": {"id:4": 35, "lvl:4": 2}}}}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.steeleafShovel", "tag:10": {"ench:9": {"0:10": {"id:4": 32, "lvl:4": 2}}}}, "3:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinDarkWizard"}, "1:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSmith"}, "2:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSurvivor"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 24, "Damage:2": 17339, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.steeleafHelm", "tag:10": {"ench:9": {"0:10": {"id:4": 4, "lvl:4": 2}}}}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.steeleafPlate", "tag:10": {"ench:9": {"0:10": {"id:4": 3, "lvl:4": 2}}}}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.steeleafLegs", "tag:10": {"ench:9": {"0:10": {"id:4": 1, "lvl:4": 2}}}}, "3:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.steeleafBoots", "tag:10": {"ench:9": {"0:10": {"id:4": 2, "lvl:4": 2}}}}}, "taskID:8": "bq_standard:retrieval"}}}