{"preRequisites:9": {"0:10": {"questIDHigh:4": -7794674471479980302, "questIDLow:4": -5399646287042766590}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "With the §1§lMothership§r (see the Space Race tab for how to build one) you can visit the truly last system: the Amun-Ra system. It has six planets and moons you can visit, each with at least one unique ore vein.\n\n\n§l§nAnubis§r\nInfinity Catalyst vein (Neutronium, Adamantium, Infinity Catalyst, Bedrockium)\n\n§l§nHorus§r\nCosmic Neutronium vein (Neutronium, Cosmic Neutronium, Black Plutonium, Bedrockium)\n\n§l§nMaahes§r\nNaquadria vein (Naquadah, Enriched Naquadah, Naquadria, Trinium)\n\n§l§nMehen Belt§r\nAwakened Draconium vein (Draconium, Draconium, Awakened Draconium, Nether Star)\n\n§l§nNeper§r\nDilithium (Dilithium, Dilithium, Mysterious Crystal, Vinteum)\n\n§l§nSeth§r\nRaw Tengam (Raw Tengam, Raw Tengam, Electrotine, Samarium)\n\n\nRaw Tengam is the vein you will want the most, as you can process Raw Tengam into Attuned Tengam, which is the UEV+ replacement for Magnetic Samarium. On Ho<PERSON> and Neper generate Certus Quartz veins (<PERSON>rt<PERSON>, <PERSON><PERSON><PERSON>, Charged Certus Qua<PERSON>z, Quartz Sand) as well §mI can't imagine why you would still need that though§r.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 110, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "The Final Frontier!", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "OR", "visibility:8": "NORMAL"}}, "questIDHigh:4": 6871186703039940952, "questIDLow:4": -5095508056809418717, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 64, "Damage:2": 110, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 64, "Damage:2": 5110, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}}, "taskID:8": "bq_standard:retrieval"}}}