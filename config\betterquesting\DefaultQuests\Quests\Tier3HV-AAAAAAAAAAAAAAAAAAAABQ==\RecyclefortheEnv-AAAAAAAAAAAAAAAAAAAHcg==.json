{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1014}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "If you have too much wood, saplings, netherrack or other garbage, you can recycle it and get scrap [note](this does not have a 100% chance)[/note]. Scrap can be used to make UUAmplifier to make UUMatter in 1/4 the time, saving you a significant amount of energy.\n\nCertain items such as cobblestone, stone, and tiny/small dusts are too easy to make and don't give any scrap. But otherwise any items can be recycled to get scrap.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 333, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§9§lRecycle for the Environment", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1906, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 64, "Damage:2": 0, "OreDict:8": "", "id:8": "IC2:itemScrapbox"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 4, "Damage:2": 331, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}}}