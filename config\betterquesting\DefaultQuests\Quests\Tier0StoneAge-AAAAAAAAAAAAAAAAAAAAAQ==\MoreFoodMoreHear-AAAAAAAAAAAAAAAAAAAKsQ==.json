{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 15}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "So, food is great, right? But what's even greater is eating tons of it! Too boring and pointless you say? Well, what if I told you, that you can get extra hearts just by expanding your palate. You get 1 point for every half shank of hunger from a food eaten, and after 50 points, you get 1 extra heart! Now you have a reason to make all those Pam's foods. Each food contributes its shank-value once. Eating something from a lunch box counts as if you took it out and ate it manually.\n\nIf you want to see what foods you've eaten, you can use the Food Journal, but it's clunky. Realistically, you want to use NEI to both see foods eaten and not eaten. You can search for 'not yet eaten' to see foods left, and 'assisted' to see foods eaten. You need to have started diminishing returns first to see this.\n\nIf it doesn't work, go to NEI options at bottom-left when the inventory is open, then click on Inventory, and change Search Mode to Regex. Then restart your client and try searching again.§r\n\nThere are also two commands:\n\n/foodlist size\nTells you the overall progress, and the progress to the next heart.\n\n/foodlist sync <PlayerName>\nSync your food progression with the server if it gets out of sync.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "SpiceOfLife:bookfoodjournal"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§2§lMore Food, More Hearts", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2737, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "harvestcraft:friedchickenItem"}}}}, "tasks:9": {"0:10": {"index:3": 0, "taskID:8": "bq_standard:checkbox"}}}