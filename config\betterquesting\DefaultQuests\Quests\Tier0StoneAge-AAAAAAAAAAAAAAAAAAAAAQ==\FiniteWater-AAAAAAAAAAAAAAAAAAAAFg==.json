{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 23}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Finite water is a problem, that's for sure. Last night, you had an idea. If you can drink cacti, you should also be able to make water out of 8 cacti and use that for most recipes that require water. How cool is that? So what about another trade? 10 wood for... 3 cacti so you can set up a farm. Deal?\n\nTo automate the farm, place a string next to the cactus and a sand on top of the string. Now when the cactus grows, it will automatically break! You can stack this horizontally and vertically to make a very efficient cactus farm. Use water to redirect the cactus to a convenient pickup location or to above a hopper.\n\nFirst player to make a base running off of only cactus water gets a cape!", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:cactus"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§2§lFinite Water!?", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 22, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 3, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:cactus"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 1, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 10, "Damage:2": 0, "OreDict:8": "logWood", "id:8": "minecraft:log"}}, "taskID:8": "bq_standard:retrieval"}}}