{"preRequisites:9": {"0:10": {"questIDHigh:4": -7439799557305581253, "questIDLow:4": -5895649105231252556}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Want even more speed on your wafer processing? Using Grade 2 Purified Water, made by ozonation, you can now reduce processing time by 50%. To run this machine, you will need to make large amounts of Ozone Gas by bombarding air with a strong laser. If you haven't already, you should definitely make the Hyper-Intensity Laser Engraver multiblock to speed up this process.\n\n[note]This multiblock is very picky about where the ozone input hatch goes. If you can't figure out why it's not forming, chances are you forgot to place this hatch in the correct location. Use the hologram projector to see where it goes.[/note]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 9404, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§c§lOzonation", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 1429520268554815440, "questIDLow:4": -9020589751289166951, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 9404, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 96, "Damage:2": 10, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings9"}, "1:10": {"Count:3": 27, "Damage:2": 9, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings9"}, "2:10": {"Count:3": 6, "Damage:2": 316, "OreDict:8": "", "id:8": "gregtech:gt.blockframes"}, "3:10": {"Count:3": 3, "Damage:2": 1, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings8"}}, "taskID:8": "bq_standard:optional_retrieval"}}}