{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1680}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 2607}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Now that your power needs are increasing, you need a way to keep up with demand. To generate over 1A ZPM with a single machine you have two options: solars, and fusion.\n\nWhile solars are simpler to set up, solars cap out at 524288 EU/t. Fusion can reach levels close to eight times that with the right setup.\n\nFor your first reactor, you will need a controller, 2-16 fluid input hatches, 1-16 fluid output hatches, and 16 energy hatches. All hatches have to be LuV tier or better. Each hatch provides 2048 EU/t for the recipe, and has a storage capacity of 10M eu.\n\nThis means you can do any fusion recipe that takes less than 32768 EU/t or less, and has a startup cost of 160M EU or less. Using more fluid hatches means you need less fusion casings. While this doesn't matter as much for the mk1 reactor, this principle helps tremendously with the higher reactor tiers.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 1193, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§l§c§lStarting Fusion", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1212, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 41, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag", "tag:10": {"ench:9": {"0:10": {"id:4": 35, "lvl:4": 3}}}}, "1:10": {"Count:3": 4, "Damage:2": 32606, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 2, "Damage:2": 32615, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "3:10": {"Count:3": 2, "Damage:2": 32635, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "4:10": {"Count:3": 2, "Damage:2": 32645, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "5:10": {"Count:3": 64, "Damage:2": 2400, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianIII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 2, "Damage:2": 56, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "1:10": {"Count:3": 1, "Damage:2": 66, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "2:10": {"Count:3": 16, "Damage:2": 46, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 32, "Damage:2": 15, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings"}, "1:10": {"Count:3": 79, "Damage:2": 6, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 1193, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}}}