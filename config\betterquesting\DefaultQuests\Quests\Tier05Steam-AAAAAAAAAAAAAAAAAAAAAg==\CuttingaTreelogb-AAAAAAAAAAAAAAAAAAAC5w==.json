{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 741}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "By making a lumber axe, you are now able to cut down an entire tree in one hit. Don't forget your backpack when you're cutting a sacred oak down.\n\nProtip: With wooden equivalents of casts, make a lumber axe completely out of netherrack - lower mining speed means less XP needed to level it up. Use the modifier slots to add Reinforced. Once you've leveled it up enough, upgrade the parts for better ones, such as steel, obzinite, or others. The higher-level metals will require an MV Extruder. Keep in mind that the auto-smelt modifier doesn't work, so don't bother with it.\n\nThe quest will accept a lumber axe from any material.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:lumberaxe", "tag:10": {"InfiTool:10": {"Accessory:4": 16, "Attack:4": 4, "BaseAttack:4": 4, "BaseDurability:4": 2193, "BonusDurability:4": 0, "Broken:4": 0, "Damage:4": 0, "Extra:4": 16, "Handle:4": 16, "HarvestLevel:4": 5, "HarvestLevel2:4": 5, "Head:4": 16, "MiningSpeed:4": 700, "MiningSpeed2:4": 700, "ModDurability:6": 0.0, "Modifiers:4": 0, "RenderAccessory:4": 16, "RenderExtra:4": 16, "RenderHandle:4": 16, "RenderHead:4": 16, "Shoddy:6": 0.0, "ToolEXP:4": 0, "ToolLevel:4": 1, "TotalDurability:4": 2193, "Unbreaking:4": 2}, "display:10": {"Name:8": "Steel Lumber Axe"}}}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§3§lCutting a Tree log by log is so Old School", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 743, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 10, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSmith"}, "1:10": {"Count:3": 10, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 17, "OreDict:8": "", "id:8": "TConstruct:metalPattern"}, "1:10": {"Count:3": 1, "Damage:2": 16, "OreDict:8": "", "id:8": "TConstruct:metalPattern"}, "2:10": {"Count:3": 1, "Damage:2": 15, "OreDict:8": "", "id:8": "TConstruct:metalPattern"}, "3:10": {"Count:3": 1, "Damage:2": 14, "OreDict:8": "", "id:8": "TConstruct:metalPattern"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:lumberaxe", "tag:10": {"InfiTool:10": {"Accessory:4": 16, "Attack:4": 4, "BaseAttack:4": 4, "BaseDurability:4": 2193, "BonusDurability:4": 0, "Broken:4": 0, "Built:4": 1, "Damage:4": 0, "Extra:4": 16, "Handle:4": 16, "HarvestLevel:4": 5, "HarvestLevel2:4": 5, "Head:4": 16, "MiningSpeed:4": 700, "MiningSpeed2:4": 700, "ModDurability:6": 0.0, "Modifiers:4": 0, "RenderAccessory:4": 16, "RenderExtra:4": 16, "RenderHandle:4": 16, "RenderHead:4": 16, "Shoddy:6": 0.0, "ToolEXP:4": 0, "ToolLevel:4": 1, "TotalDurability:4": 2193, "Unbreaking:4": 2}, "display:10": {"Name:8": "Lumber Axe"}}}}, "taskID:8": "bq_standard:retrieval"}}}