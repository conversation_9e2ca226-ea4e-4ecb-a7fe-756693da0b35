{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1198}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "To make Oganesson, you'll need to make Californium. To make that, you'll need Beryllium and Plutonium 239, all in the Fusion Reactor. After the two fusion chains, you'll have the best noble gas in the pack, which is used in several places.\n\nIn this tier, you can send it to Void Miners, to increase their ore production speed, or EBFs, to make them run faster, although the EBF option is too weak right now (changes coming soon). Later on, Oganesson will be used in other places, like the optical circuit line, or to produce Metastable Oganesson, which is needed in much larger quantities later on.\n\nOganesson replication is on the way out, and the fusion recipe has improved a lot, so there is no longer an incentive to replicate and your Fusion Reactor should keep running for Og.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 38, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "I Put Fusion in Your Fusion so you Can Fusion While You Fusion", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1202, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 8, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.99"}, "1:10": {"Count:3": 1, "Damage:2": 100, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.99"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 39, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcellMolten"}, "1:10": {"Count:3": 1, "Damage:2": 40, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcellMolten"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 38, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}}, "taskID:8": "bq_standard:retrieval"}}}