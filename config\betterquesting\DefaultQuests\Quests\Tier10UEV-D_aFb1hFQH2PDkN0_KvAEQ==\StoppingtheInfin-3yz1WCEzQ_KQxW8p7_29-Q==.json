{"preRequisites:9": {"0:10": {"questIDHigh:4": -6936489808588683341, "questIDLow:4": -5302417245690018536}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 2590}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Already struggling to keep up with infinity demand? Not to worry, the component assembly line may just be what you need. You probably have one already. Upgrade it to UEV to allow creating UEV components at a significantly reduced cost.", "globalShare:1": 0, "icon:10": {"Count:3": 64, "Damage:2": 9, "OreDict:8": "", "id:8": "GoodGenerator:componentAssemblylineCasing"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Stopping the Infinity Bleed", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": -2365245945413417998, "questIDLow:4": -8014877735925203463, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 43, "Damage:2": 9, "OreDict:8": "", "id:8": "GoodGenerator:componentAssemblylineCasing"}}, "taskID:8": "bq_standard:retrieval"}}}