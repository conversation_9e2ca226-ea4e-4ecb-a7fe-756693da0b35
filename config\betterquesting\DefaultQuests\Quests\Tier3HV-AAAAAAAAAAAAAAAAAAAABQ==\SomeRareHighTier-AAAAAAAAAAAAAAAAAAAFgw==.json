{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 851}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 854}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Platinum, Palladium, Iridium and Osmium can be extracted with a chemical reaction from Chalcopyrite and Pentlandite using sulfuric acid. You don't have the technology to process them properly right now but later on you need large amounts of them.\n\nNote that this is not the proper method to get rare metals. This means none of them are pure, and cannot be utilized without the platline method, aside from EBF-ing platinum. Proper Platinum processing is EV, but you'll have to wait until IV to use the rest. Collecting a lot of it now (especially using this method) might not be a great idea depending on how long that will be.\n\n[note]A Chemical Bath also extracts Platinum from Nickel Ore if you use Mercury. Time to head back to the Twilight Forest![/note]", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 2241, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§9§lSome Rare High Tier Metals", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1411, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 16, "Damage:2": 30659, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 1, "Damage:2": 6, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}, "2:10": {"Count:3": 64, "Damage:2": 6855, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinChemistI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 108, "Damage:2": 30720, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 54, "Damage:2": 6855, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 54, "Damage:2": 6909, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 12, "Damage:2": 2241, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 12, "Damage:2": 2837, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 12, "Damage:2": 2086, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 24, "Damage:2": 47, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGenerateddust"}, "3:10": {"Count:3": 8, "Damage:2": 53, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGenerateddust"}, "4:10": {"Count:3": 2, "Damage:2": 70, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGenerateddust"}, "5:10": {"Count:3": 2, "Damage:2": 69, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGenerateddust"}}, "taskID:8": "bq_standard:optional_retrieval"}}}