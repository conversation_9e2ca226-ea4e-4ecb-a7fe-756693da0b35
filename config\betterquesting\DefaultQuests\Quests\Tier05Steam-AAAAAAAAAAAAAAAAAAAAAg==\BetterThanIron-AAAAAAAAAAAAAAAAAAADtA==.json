{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 65}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "But not as good as steel. Mostly, this is used in machine hulls, or in tools if you don't want to use steel up for that.\n\nTo make wrought iron, you need a tinkers smelter to melt some iron ingots down and cast nuggets; or use an ingot and a saw to get your nuggets. Now put these in a furnace to get wrought iron nuggets. With the compressor, you can compress the nuggets into ingots.\n\n§3Hint: Crushed or purified iron ore (not a mixed version like pyrite, look for Iron Ore in Chalcopyrite veins) can be smelted to iron nuggets directly. Or you can use an alloy smelter to turn iron ingots into nuggets faster than a smeltery. You can also use a saw to cut up iron ingots into nuggets.\n\nLater on, you can use an arc furnace to make wrought iron ingots directly.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 11304, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§3§lBetter Than Iron", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 948, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 12, "OreDict:8": "", "id:8": "gregtech:gt.metatool.01", "tag:10": {"GT.ToolStats:10": {"MaxDamage:4": 38400, "PrimaryMaterial:8": "WroughtIron", "SecondaryMaterial:8": "<PERSON>"}, "ench:9": {"0:10": {"id:2": 16, "lvl:2": 2}}}}, "1:10": {"Count:3": 1, "Damage:2": 16, "OreDict:8": "", "id:8": "gregtech:gt.metatool.01", "tag:10": {"GT.ToolStats:10": {"MaxDamage:4": 38400, "PrimaryMaterial:8": "WroughtIron", "SecondaryMaterial:8": "WroughtIron"}}}, "2:10": {"Count:3": 1, "Damage:2": 32243, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.02"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 10, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 63, "Damage:2": 9032, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 63, "Damage:2": 9304, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 7, "Damage:2": 11304, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}}}