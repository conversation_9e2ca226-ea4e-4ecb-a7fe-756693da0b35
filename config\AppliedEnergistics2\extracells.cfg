# Configuration file

integration {
    # Enable AE2 Wireless Crafting Terminal Integration.
    B:"enableAE2 Wireless Crafting Terminal"=true

    # Enable BuildCraftFuel Integration.
    B:enableBuildCraftFuel=true

    # Enable IngameWikiMod Integration.
    B:enableIngameWikiMod=false

    # Enable Mekanism Integration.
    B:enableMekanism=true

    # Enable MekanismGas Integration.
    B:enableMekanismGas=true

    # Enable NotEnoughItems Integration.
    B:enableNotEnoughItems=true

    # Enable OpenComputers Integration.
    B:enableOpenComputers=true

    # Enable Thaumatic Energistics Integration.
    B:"enableThaumatic Energistics"=true

    # Enable Waila Integration.
    B:enableWaila=true
}


"storage cells" {
    # Should the mount of bytes needed for a new type depend on the cellsize?
    B:dynamicTypes=true
}


tooltips {
    # Shall the guis shorten large mB values?
    B:shortenedBuckets=true
}


