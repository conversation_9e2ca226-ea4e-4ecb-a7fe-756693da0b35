{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 36}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 494}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Are you wondering how to process raw redstone ore without a macerator?\n\nUse the hammer in the crafting table to get some crushed redstone ore from your raw redstone ore. After that, use the hammer in the crafting table again to get some impure redstone dust.\n\nLastly, fill a cauldron with water and drop the whole stack of dust into it, either manually or by pressing Ctrl+Q. The cauldron can handle the entire stack with the same amount of water. You will get pure redstone dust for your redstone needs.\n\n§3You could also take the hammer with you on your mining trip and break the redstone ore with it directly to get crushed redstone ore, thus skipping the raw redstone ore.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 5810, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§1§2§lRedstone, Early Game Processing", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 535, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}, "1:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:bucket"}, "2:10": {"Count:3": 10, "Damage:2": 810, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:cauldron"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:water_bucket"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 64, "Damage:2": 5810, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:optional_retrieval"}, "3:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 3, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 64, "Damage:2": 3810, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:optional_retrieval"}, "4:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 4, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 64, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:redstone"}}, "taskID:8": "bq_standard:retrieval"}}}