{"preRequisites:9": {"0:10": {"questIDHigh:4": -6962518690474080185, "questIDLow:4": -6893323685044150631}, "1:10": {"questIDHigh:4": 1930161895658832038, "questIDLow:4": -9020796615899025430, "type:1": 1}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "As understanding of graviton shards continues to improve, better control over the materials they manipulate can be achieved. Graviton shards provide an alternative to electromagnetic containment of plasma using spacetime manipulation instead, allowing the Forge to directly output processed materials as plasma.\n\n[note]The third module of the Godforge, Heliothermal Plasma Fabricator can directly convert materials into their plasma form, at the cost of an immense amount of power. This may not be ideal for every plasma, but can save a lot of time on particularly slow ones. Additionally, later upgrades allow the creation of exotic plasmas which cannot be created through other means.[/note]\n\n[note]Usage of this module requires the GPCI upgrade.[/note]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 15414, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§b§lHeliothermal Plasma Fabricator", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": -7295994264991546955, "questIDLow:4": -5675835535531457502, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 15414, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 20, "Damage:2": 0, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "1:10": {"Count:3": 20, "Damage:2": 2, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "2:10": {"Count:3": 5, "Damage:2": 8, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "3:10": {"Count:3": 5, "Damage:2": 1, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "4:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}}, "taskID:8": "bq_standard:optional_retrieval"}}}