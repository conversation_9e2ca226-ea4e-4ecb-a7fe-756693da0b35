{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 146}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 142}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "The inventory panel is an alternative to a PR or LP storage system. Just like PR, you have to connect every chest/barrel with an item conduit.\n\nIn addition to that, you also have to install a \"Remote Awareness\" upgrade into every conduit side you want the panel to see, and provide the panel with Nutrient Distillation which you make in The Vat.\n\nWell, they're all obsolete once you get AE2 though.\n", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:blockInventoryPanel"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§9§lI See Everything", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 163, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemFunctionUpgrade"}, "1:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "IronChest:BlockIronChest"}}}, "1:10": {"choices:9": {"0:10": {"Count:3": 16, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemItemConduit"}, "1:10": {"Count:3": 4, "Damage:2": 4, "OreDict:8": "", "id:8": "IronChest:BlockIronChest"}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemFunctionUpgrade"}}, "ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:choice"}, "2:10": {"ignoreDisabled:1": 0, "index:3": 2, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianI"}, "1:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinDarkWizardI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:blockInventoryPanel"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemFunctionUpgrade"}}, "taskID:8": "bq_standard:retrieval"}}}