{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 162}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "EV motors need Neodymium. Rare Earth is an §ook§r source, but on the Moon you can find a vein with pure Neodymium ore. Bastnasite is a great source of fluorine. And Monazite can be taken apart for more Rare Earth.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 67, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§9§lFind Ba<PERSON>nasite, Monazite and Neodymium", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "OR", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1612, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 6, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSpaceII"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinAdventureII"}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 64, "Damage:2": 905, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}, "1:10": {"Count:3": 32, "Damage:2": 520, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}, "2:10": {"Count:3": 32, "Damage:2": 67, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 64, "Damage:2": 5905, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}, "1:10": {"Count:3": 32, "Damage:2": 5520, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}, "2:10": {"Count:3": 32, "Damage:2": 5067, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}}, "taskID:8": "bq_standard:retrieval"}}}