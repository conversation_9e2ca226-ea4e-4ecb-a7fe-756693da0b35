{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 493}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Once you have the required materials, you will need to find an ocelot to tame.\n\nOcelots can be found in Jungle Biomes.\n\nIf you are having trouble finding an ocelot, you can always summon an ocelot using a spawn egg.\n\nYou can tame an ocelot by giving it a raw fish. So add the raw fish to your inventory and select the raw fish in your hotbar.\n\nJust like vanilla Minecraft, a tamed ocelot turns into a cat. The cat will repel creepers.\n\n§3TIP: Make sure you are in a large space when trying to tame the ocelot so that the ocelot has lots of room to move. If you are in a space that is too small, you will not be able to tame the ocelot.\n\nNext, while holding the raw fish, do not move. If you move, you will scare away the ocelot. If you are very still, the ocelot will eventually notice the fish in your hand and begin to walk over to you.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:fish"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§3§lTame an Ocelot to Get a Friend", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2346, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 32, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:fence"}, "1:10": {"Count:3": 32, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:fish"}, "2:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 10, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSurvivor"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:fishing_rod"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 32, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:fence"}, "1:10": {"Count:3": 16, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:fish"}}, "taskID:8": "bq_standard:retrieval"}}}