{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 66}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 834}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 835}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Steam machines are quite stupid, they just do one thing over and over again. As you advance into the electrical age, you've decided that you want the machines to have more features, like automatic output in order to create some automation. For that, you need electronic circuits.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "IC2:itemPartCircuit"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§3§lYour First Electronic Circuit", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 68, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "harvestcraft:icecreamItem"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 15, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}}}}, "tasks:9": {"0:10": {"allowAnvil:1": 0, "allowCraft:1": 1, "allowCraftedFromStatistics:1": 0, "allowSmelt:1": 1, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "IC2:itemPartCircuit"}}, "taskID:8": "bq_standard:crafting"}}}