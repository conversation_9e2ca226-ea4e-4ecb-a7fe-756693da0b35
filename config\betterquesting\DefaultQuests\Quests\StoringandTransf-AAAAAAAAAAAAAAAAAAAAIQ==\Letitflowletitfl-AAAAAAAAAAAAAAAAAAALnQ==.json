{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2971}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "... can't hold it back anymore. It is time to collect the rewards of your hard work. Connect the fluid output hatch from the dam to the fluid input hatch of this turbine and finally enjoy the power! You can pause this multiblock if you cut the fluid input or use a machine controller. Oh, and you will get your water back in the fluid output hatch.\n\nCongratulations to making it this far! Enjoy your ginormous energy buffer!\n\nIf you have any further questions, missing information or suggestions please do not hesitate to post on the official GT New Horizons Discord. Please add '@SinTh0r4s' to your post to make sure I'll see it.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 17019, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Let it flow, let it floww ...", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2973, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 16, "Damage:2": 5133, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "1:10": {"Count:3": 1, "Damage:2": 32730, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 1, "Damage:2": 5, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 17019, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 51, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "1:10": {"Count:3": 1, "Damage:2": 61, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "2:10": {"Count:3": 1, "Damage:2": 90, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "3:10": {"Count:3": 1, "Damage:2": 32, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 21, "Damage:2": 1, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings2"}}, "taskID:8": "bq_standard:optional_retrieval"}, "3:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 3, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "structurelib:item.structurelib.constructableTrigger"}}, "taskID:8": "bq_standard:optional_retrieval"}}}