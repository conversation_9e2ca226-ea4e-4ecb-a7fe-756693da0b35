{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 384}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 376}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 381}, "3:10": {"questIDHigh:4": 0, "questIDLow:4": 382}, "4:10": {"questIDHigh:4": 0, "questIDLow:4": 383}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Ok, I think I have everything I need to assemble the device. Can you assist me with some additional materials? I need to update some of my machines, as these exotic components you got me damaged some of them already. Don't worry, I have a list here.\n\nSome of my circuits burned to a crisp while I was trying to measure the conductivity of these soul and life shards. I need better ones.\n\nGood lord, these mysterious crystals have eaten away my diamond grinding head! How could that happen? Can you get me a better one?\n\nDo you see that pile of Rhodium and Palladium over there? No? Neither do I. Because the machine was VAPORIZED! Good that you gave me 2 singularities, the first one... Well... Just get me a better casing, would you?\n\nIt seems, that the stabilizers need... stabilizing... But I'm out of field generators. Can you help me with this?", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 17, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "A Few More Things...", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "SECRET"}}, "questIDHigh:4": 0, "questIDLow:4": 385, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 2, "Damage:2": 42, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianIII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 1, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 10, "Damage:2": 32088, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 1, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 32723, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 1, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 17, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "3:10": {"autoConsume:1": 0, "consume:1": 1, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 3, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 2, "Damage:2": 32675, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}}}