{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 784}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 1894}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 1740}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Once you have a GT boiler or Large Combustion Engine with a long startup time, you may wish to automate turning it on/off to save fuel, since extra power or Steam will just be voided. Additionally, the bigger your Steam/EU buffer is, the less time your generators will spend heating up, which makes them more efficient.\n\nUse a Machine Controller Cover on the controller for that. You can put it on top, bottom, or sides next to a casing - casings will transmit the redstone signal. You can use a Soldering Iron and Fine Soldering Alloy Wire to set the Redstone Output of a controller block to strong. When set to Strong it can go through a casing, but not hatches, buses, or other blocks. Use it with an activity detector if you want to receive a signal from the controller rather than turning it on/off with one.\n\nWith a Machine Controller Cover, you can use redstone to turn the machine on and off. This is handy if you don't want to use a Soft Hammer to disable a machine yourself constantly.\n\nThe best way to do that is with an RS latch. One input will turn the machine on, and one input will turn the machine off. Use a comparator attached to a valve to measure the steam level in your multiblock Iron or Steel Tank. Invert the signal to turn on your boiler when the steam level is low. When the steam level signal is high, turn your boiler off. Use a trail of redstone to lower the signal strength. Because of boiler/turbine warmup, try to set the ON strength to 30 percent of capacity and OFF strength to about 90 percent.\n\nRed alloy wire will hold a redstone signal for a longer distance than normal redstone, while redstone conduits will go indefinitely. You can also use amplified redcrystal to improve the strength of the signal for a long distance, repeating as necessary.\n\nYou can use an Energy Detector Cover in Normal Electrical Storage (including batteries) mode to monitor the power in a battery buffer and use it to enable/disable generators in a similar way. Change mode using a screwdriver. If you use a dense redcrystal, it will only allow signal through at a certain level, perfect for automating this kind of thing.\n\nFor a video version, check out this video: [url]https://www.youtube.com/watch?v=jPfnifJEO64[/url]", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 32730, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§6§lAutomating Power On/Off", "partySingleReward:1": 0, "questLogic:8": "OR", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1654, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 2, "Damage:2": 32734, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianI"}, "1:10": {"Count:3": 2, "Damage:2": 4, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag", "tag:10": {"ench:9": {"0:10": {"id:4": 35, "lvl:4": 3}}}}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 32730, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 1, "Damage:2": 12, "OreDict:8": "", "id:8": "ProjRed|Integration:projectred.integration.gate"}, "2:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "ProjRed|Integration:projectred.integration.gate"}, "3:10": {"Count:3": 16, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:redstone"}, "4:10": {"Count:3": 8, "Damage:2": 0, "OreDict:8": "", "id:8": "ProjRed|Transmission:projectred.transmission.wire"}}, "taskID:8": "bq_standard:retrieval"}}}