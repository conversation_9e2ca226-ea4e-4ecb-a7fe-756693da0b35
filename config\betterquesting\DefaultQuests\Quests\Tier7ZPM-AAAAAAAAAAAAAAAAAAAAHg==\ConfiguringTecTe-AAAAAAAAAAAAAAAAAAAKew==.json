{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2005}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Many TecTech multis can be customized by adjusting certain internal numbers called parameters. You can directly adjust them in the GUI.\n\nIf there are lit up sections in the parameters part of the multi interface (see the TecTech Multiblocks quest), you can configure the multi, otherwise there is nothing to do.\n\nWe'll be using the Quantum Computer as our example, since that is what you probably want it for.\n\nFirst we need to understand the parameter naming, like ID:0:1:I. The first number represents the parameter block. For the QC only block 0 is used. Each such block can have up to 4 used parameters, 2 inputs and 2 outputs. The letter at the end specifies if a parameter is an input or output with I or O and the number in the middle distinguishes the 2 parameters of this kind in the block. It is either 0 or 1.\n\nInputs are something that is either set or is a default. Outputs give information that the machine sends to you, that indicates things. (like heat or Computation)\n\nAnd lastly, the Parametrizer Memory Card lets you copy settings from one machine to another easily.", "globalShare:1": 0, "icon:10": {"Count:3": 0, "Damage:2": 0, "OreDict:8": "", "id:8": "tectech:item.em.parametrizerMemoryCard"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Configuring TecTech Multiblocks", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2683, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "tectech:item.em.parametrizerMemoryCard", "tag:10": {}}}, "taskID:8": "bq_standard:retrieval"}}}