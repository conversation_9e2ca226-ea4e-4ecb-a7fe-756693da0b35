{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1614}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Rare Earth processing may have been ignored, but you believe your new Dehydrator can finally make it worthwhile. First, you need to use some Sulfuric Acid to melt off any impurities in a Chemical Bath, leaving you with Crushed Rare Earth (I). After that you need to process it like a normal ore until you get Rare Earth (I) Dust.\n\nAfter running the Dust through your Dehydrator, you get a number of strange materials, only some of which you recognize. But if you run these materials through an Electrolyzer or through the Dehydrator again, you may get some use out of them. Some of them may take quite a few to do so, but you §o§o§r§h§ohave§r been stockpiling, right? You'll need 204 Rare Earth.\n\nYou believe if you had more powerful acids and machines, you could extract even more valuable materials out of the Rare Earth.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "miscutils:itemDustRareEarthI"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§6§lEven Rarer Earths", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2409, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 2, "Damage:2": 5, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}, "1:10": {"Count:3": 64, "Damage:2": 2891, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 32, "Damage:2": 0, "OreDict:8": "", "id:8": "miscutils:itemDustRareEarthI"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinChemistII"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSmithII"}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "miscutils:itemDustGreenockite"}, "1:10": {"Count:3": 11, "Damage:2": 0, "OreDict:8": "", "id:8": "miscutils:itemDustLanthaniteCe"}, "2:10": {"Count:3": 45, "Damage:2": 0, "OreDict:8": "", "id:8": "miscutils:itemDustAgarditeCd"}, "3:10": {"Count:3": 13, "Damage:2": 0, "OreDict:8": "", "id:8": "miscutils:itemDustYttrialite"}, "4:10": {"Count:3": 1, "Damage:2": 2522, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "5:10": {"Count:3": 2, "Damage:2": 2830, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "6:10": {"Count:3": 4, "Damage:2": 2855, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "7:10": {"Count:3": 10, "Damage:2": 0, "OreDict:8": "", "id:8": "miscutils:itemDustCryoliteF"}, "8:10": {"Count:3": 1, "Damage:2": 2045, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}}}