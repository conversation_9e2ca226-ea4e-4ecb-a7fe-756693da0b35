{"preRequisites:9": {"0:10": {"questIDHigh:4": -199482370913580560, "questIDLow:4": -9064439369025459403}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Absolute Baryonic Perfection! This multiblock allows you to create water that is pure even on the subatomic level. Not a single quark is out of place in Grade 8 Purified Water. Thanks to this purity, even more advanced processes are possible, starting with reducing the processing time of Optical Boules and increasing yield on Optical Chips.\n\nWhile the tooltip is certainly a lot less intimidating than the one of the Degasser, automating this process will not be an easy challenge. Have fun solving the puzzle!", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 9414, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Absolute Purity", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 7842301023653611291, "questIDLow:4": -6269476597080921136, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 9414, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings10"}, "1:10": {"Count:3": 1, "Damage:2": 15, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings9"}, "2:10": {"Count:3": 1, "Damage:2": 3, "OreDict:8": "", "id:8": "gregtech:gt.blockglass1"}, "3:10": {"Count:3": 1, "Damage:2": 14, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings9"}, "4:10": {"Count:3": 1, "Damage:2": 395, "OreDict:8": "", "id:8": "gregtech:gt.blockframes"}}, "taskID:8": "bq_standard:optional_retrieval"}}}