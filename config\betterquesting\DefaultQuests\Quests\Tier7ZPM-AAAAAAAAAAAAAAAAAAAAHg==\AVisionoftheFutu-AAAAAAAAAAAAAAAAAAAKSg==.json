{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2627}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "To get to the Toxic Everglades, you'll need 10 Containment Frames and an Alkalus Disk (Activated). You use them like when making a nether portal. The disk can also be used to set special fires. It doesn't spread across the ground that well, but it'll take out a tree real fast. If you want to get rid of a giant tree, this is your tool.\n\nThe dim itself is filled with GT++ ores. If you want the ore distribution to make sense/be greggy, badger Alk about it.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "ToxicEverglades:everglades.trigger"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "A Vision of the Future (FPS Drop Not Included)", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2634, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 10, "Damage:2": 0, "OreDict:8": "", "id:8": "ToxicEverglades:blockDarkWorldPortalFrame"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "ToxicEverglades:everglades.trigger"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"biome:3": -1, "dimension:3": 227, "hideInfo:1": 0, "index:3": 1, "invert:1": 0, "name:8": "Toxic Everglades", "posX:3": 0, "posY:3": 0, "posZ:3": 0, "range:3": -1, "structure:8": "", "taskID:8": "bq_standard:location", "taxiCabDist:1": 0, "visible:1": 0}}}