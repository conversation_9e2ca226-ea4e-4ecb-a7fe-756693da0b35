{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 146}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 1231}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Tired of the Multifarm's requirements? The EnderIO Farming Station is here to ease your suffering. It only requires EU and tools and can be easily upgraded with capacitors. Most standard crops and trees work with it, but unfortunately, IC2 crops do not.\n\n[note]If you want to use it for farming crops from Pam's HarvestCraft, you'll need to provide the crop itself, not the seed, for it to work properly.[/note]", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:blockFarmStation"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§9§lFarming a Little Easier", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1739, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:diamond_hoe", "tag:10": {"ench:9": {"0:10": {"id:4": 34, "lvl:4": 3}, "1:10": {"id:4": 35, "lvl:4": 3}}}}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:diamond_axe", "tag:10": {"ench:9": {"0:10": {"id:4": 35, "lvl:4": 3}, "1:10": {"id:4": 34, "lvl:4": 3}}}}, "2:10": {"Count:3": 3, "Damage:2": 6, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag", "tag:10": {"ench:9": {"0:10": {"id:4": 35, "lvl:4": 3}}}}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 22378, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 2, "Damage:2": 5, "OreDict:8": "", "id:8": "EnderIO:itemMaterial"}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemMachinePart"}, "3:10": {"Count:3": 2, "Damage:2": 32652, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "4:10": {"Count:3": 2, "Damage:2": 31365, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.02"}, "5:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "EnderIO:itemFrankenSkull"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:blockFarmStation"}}, "taskID:8": "bq_standard:retrieval"}}}