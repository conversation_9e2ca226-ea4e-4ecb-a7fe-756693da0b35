{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 139}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 141}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "In order to upgrade one armor item with basic upgrades, you need a vibrant crystal and 10 xp levels. Some upgrades can be applied directly, but more advanced ones will require empowering the armor first.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:item.darkSteel_chestplate"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Dark Steel Armor Basic Upgrades", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 991, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 16, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:waterlily"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:piston"}, "2:10": {"Count:3": 36, "Damage:2": 2, "OreDict:8": "", "id:8": "Forestry:craftingMaterial"}, "3:10": {"Count:3": 1, "Damage:2": 8198, "OreDict:8": "", "id:8": "minecraft:potion"}, "4:10": {"Count:3": 1, "Damage:2": 8194, "OreDict:8": "", "id:8": "minecraft:potion"}, "5:10": {"Count:3": 1, "Damage:2": 6, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSurvivorI"}, "1:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSmithI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:item.darkSteel_helmet"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:item.darkSteel_chestplate"}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:item.darkSteel_leggings"}, "3:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:item.darkSteel_boots"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 4, "Damage:2": 6, "OreDict:8": "", "id:8": "EnderIO:itemMaterial"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:item.darkSteel_helmet", "tag:10": {"enderio.darksteel.upgrade.energyUpgrade:10": {"capacity:4": 100000, "energy:4": 100000, "level_cost:4": 10, "maxInput:4": 1000, "maxOuput:4": 1000, "unlocalized_name:8": "enderio.darksteel.upgrade.empowered_one", "upgradeItem:10": {"Count:4": 1, "Damage:4": 6}}}}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:item.darkSteel_chestplate", "tag:10": {"enderio.darksteel.upgrade.energyUpgrade:10": {"capacity:4": 100000, "energy:4": 100000, "level_cost:4": 10, "maxInput:4": 1000, "maxOuput:4": 1000, "unlocalized_name:8": "enderio.darksteel.upgrade.empowered_one", "upgradeItem:10": {"Count:4": 1, "Damage:4": 6}}}}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:item.darkSteel_leggings", "tag:10": {"enderio.darksteel.upgrade.energyUpgrade:10": {"capacity:4": 100000, "energy:4": 100000, "level_cost:4": 10, "maxInput:4": 1000, "maxOuput:4": 1000, "unlocalized_name:8": "enderio.darksteel.upgrade.empowered_one", "upgradeItem:10": {"Count:4": 1, "Damage:4": 6}}}}, "3:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:item.darkSteel_boots", "tag:10": {"enderio.darksteel.upgrade.energyUpgrade:10": {"capacity:4": 100000, "energy:4": 100000, "level_cost:4": 10, "maxInput:4": 1000, "maxOuput:4": 1000, "unlocalized_name:8": "enderio.darksteel.upgrade.empowered_one", "upgradeItem:10": {"Count:4": 1, "Damage:4": 6}}}}}, "taskID:8": "bq_standard:retrieval"}}}