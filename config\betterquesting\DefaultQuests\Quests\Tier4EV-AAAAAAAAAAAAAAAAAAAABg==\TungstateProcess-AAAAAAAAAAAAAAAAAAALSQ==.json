{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2980}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "First, let's turn our Tungstate into Scheelite. When you can get Scheelite ore, you can just skip this step.\n\nNote that if you're processing Huebnerite or Ferberite from ross128b or 128ba, the first step is slightly different.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 2910, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§a§lTungstate Processing - Step 1", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2889, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinChemistII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 42, "Damage:2": 2841, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 12, "Damage:2": 2017, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 24, "Damage:2": 1, "OreDict:8": "", "id:8": "IC2:itemCellEmpty"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 12, "Damage:2": 2006, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 6, "Damage:2": 29904, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 6, "Damage:2": 29904, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "1:10": {"Count:3": 18, "Damage:2": 63, "OreDict:8": "dustCalciumChloride", "id:8": "bartworks:gt.bwMetaGenerateddust"}}, "taskID:8": "bq_standard:optional_retrieval"}, "3:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 3, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 36, "Damage:2": 2910, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 24, "Damage:2": 2817, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}}}