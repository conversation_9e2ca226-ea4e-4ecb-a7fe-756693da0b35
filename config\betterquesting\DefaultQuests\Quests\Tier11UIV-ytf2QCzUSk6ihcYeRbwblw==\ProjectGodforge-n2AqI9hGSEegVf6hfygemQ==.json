{"preRequisites:9": {"0:10": {"questIDHigh:4": -1663909569982545142, "questIDLow:4": -4646567102207181371}, "1:10": {"questIDHigh:4": 2345791078736349427, "questIDLow:4": -8332322836118716550}, "2:10": {"questIDHigh:4": 6393847662955875738, "questIDLow:4": -8810543536998436147}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "With your research now complete, you have finally constructed it - the Forge of the Gods.\n\nThe Forge of the Gods is an immensely powerful structure constructed around a stabilized neutron star - it is so advanced that its full capabilities are not yet understood. However, through continued use, one can slowly upgrade and expand the range of abilities of the Forge, and learn the power hidden in the most extreme parts of the universe: [note]Graviton Shards[/note].\n\nThis esoteric material can only be found where conventional matter and degenerate neutronium crust matter on the surface of a neutron star meet. At this point in space, gravitons are far more common and irradiate this material mixture to create highly unstable graviton shards, which can be used to internally upgrade the Forge. While these shards cannot yet exist outside the extreme conditions of the Forge, with continued research and utilization it may be possible to eventually isolate and extract them outside the Forge - but for what purpose?\n\n[note]Click on the logo in the UI to read more on how to operate the Godforge.[/note]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 15411, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§b§lProject Godforge", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": -6962518690474080185, "questIDLow:4": -6893323685044150631, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 15411, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 3943, "Damage:2": 3, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "1:10": {"Count:3": 2819, "Damage:2": 0, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "2:10": {"Count:3": 272, "Damage:2": 1, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "3:10": {"Count:3": 130, "Damage:2": 2, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "4:10": {"Count:3": 9, "Damage:2": 0, "OreDict:8": "", "id:8": "tectech:tile.spatiallyTranscendentGravitationalLens"}, "5:10": {"Count:3": 345, "Damage:2": 5, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "6:10": {"Count:3": 36, "Damage:2": 4, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}}, "taskID:8": "bq_standard:optional_retrieval"}}}