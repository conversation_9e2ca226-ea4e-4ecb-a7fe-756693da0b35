{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2449}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Now you can use Polyphenylene Sulfide Foils and Silicone or Styrene-Butadiene Rubber to coat higher tier cables.\nYou'll need both Tungsten and Platinum in IV-tier.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 1686, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§a§lHigher Tier Cables", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2450, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 2, "Damage:2": 1687, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "1:10": {"Count:3": 10, "Damage:2": 1646, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianII"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinChemistII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 2, "Damage:2": 1680, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "1:10": {"Count:3": 2, "Damage:2": 1640, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "2:10": {"Count:3": 4, "Damage:2": 29631, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "3:10": {"Count:3": 2, "Damage:2": 471, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.99"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 2, "Damage:2": 1680, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "1:10": {"Count:3": 2, "Damage:2": 1640, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "2:10": {"Count:3": 4, "Damage:2": 29631, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "3:10": {"Count:3": 1, "Damage:2": 635, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.99"}, "4:10": {"Count:3": 1, "Damage:2": 2649, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 2, "Damage:2": 1686, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "1:10": {"Count:3": 2, "Damage:2": 1646, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}}}