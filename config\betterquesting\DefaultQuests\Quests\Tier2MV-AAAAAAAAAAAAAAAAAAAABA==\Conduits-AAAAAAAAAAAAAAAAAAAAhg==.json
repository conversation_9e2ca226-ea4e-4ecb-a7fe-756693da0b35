{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 128, "type:1": 1}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 1598}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Probably the most awesome way to transport just about anything in multiple different ways. Get some conduits of each type. Higher tiers of each (except item) exist.\n\n[note]There's also ME conduits for when you can do that.[/note]", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemPowerConduit"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§6§lConduits", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 134, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemItemConduit"}, "1:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemLiquidConduit"}, "2:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemRedstoneConduit"}, "3:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemPowerConduit"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 8, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemItemConduit"}, "1:10": {"Count:3": 8, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemLiquidConduit"}, "2:10": {"Count:3": 8, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemPowerConduit"}, "3:10": {"Count:3": 8, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemRedstoneConduit"}}, "taskID:8": "bq_standard:retrieval"}}}