{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2628}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 2633}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 2623}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "A MK II is nice, but why not make a MK III? This time you will need UV or above hatches, as well as the Fusion Machine Casing MK II.\n\nEach of the up to 16 UV Energy Hatches adds 32768 EU/t power input and 40 MEU starting capacity. Therefore this reactor can handle recipes up to 640M EU at startup.\n\nWhen using it for lower tier recipes, the reactor will double the power needed per recipe, while cutting the processing time in half, both for each tier difference.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 1195, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Upgrading the Reactor", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1198, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 4, "Damage:2": 32094, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}, "1:10": {"Count:3": 4, "Damage:2": 32608, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 2, "Damage:2": 32617, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "3:10": {"Count:3": 2, "Damage:2": 32637, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "4:10": {"Count:3": 2, "Damage:2": 32647, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "5:10": {"Count:3": 64, "Damage:2": 2440, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianIII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 2, "Damage:2": 58, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "1:10": {"Count:3": 1, "Damage:2": 68, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "2:10": {"Count:3": 1, "Damage:2": 48, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 32, "Damage:2": 7, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings4"}, "1:10": {"Count:3": 32, "Damage:2": 8, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings4"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 1195, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}}}