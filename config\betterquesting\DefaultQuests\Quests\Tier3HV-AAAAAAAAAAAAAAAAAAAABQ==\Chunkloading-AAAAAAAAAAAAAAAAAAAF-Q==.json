{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 770}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "With your HV assembler you can craft a world anchor. So let's make one. It will again load a 3x3 chunk area.\n\nYou need coins to power the world anchor, it will run a varying amount depending on the coin used.\n\n§aPersonal Anchors§r are loaded once the owner visits, and stay loaded while the player is logged in.\n\n§9Passive Anchors§r are loaded once the owner visits, and stay loaded until the server resets.\n\n[warn]World Anchors[/warn] are always loaded, even after a server reset.\n\n[note]Check with server operators in case world anchors are disabled. On the official servers, world anchors are currently disabled to keep TPS high.[/note]", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "Railcraft:machine.alpha"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§9§lChunkloading", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1529, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinChunkloaderTierII"}, "1:10": {"Count:3": 1, "Damage:2": 6, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "Railcraft:machine.alpha"}}, "taskID:8": "bq_standard:retrieval"}}}