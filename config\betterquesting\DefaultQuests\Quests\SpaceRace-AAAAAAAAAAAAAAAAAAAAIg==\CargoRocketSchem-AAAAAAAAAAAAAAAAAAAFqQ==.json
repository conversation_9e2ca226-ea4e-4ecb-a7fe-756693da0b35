{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1446}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "The Cargo Rocket has the capability to transport solid items between dimensions, and is a perfect way to transport items between dimensions without relying on ender chests.\n\nThis rocket can be loaded with 2000L of Rocket Fuel. The safe fuel level is 80 percent.\n\nYou can fill up the right slots with chests to increase the storage space to 36 and 54 slots.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "GalacticraftMars:item.schematic"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Cargo Rocket Schematic", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1449, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 3, "OreDict:8": "", "id:8": "GalacticraftMars:item.null"}, "1:10": {"Count:3": 8, "Damage:2": 11316, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 1, "Damage:2": 7, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "GalacticraftMars:item.schematic"}}, "taskID:8": "bq_standard:retrieval"}}}