{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1880}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Using the scanner, you can scan certain items (check the replicator's NEI recipes) to store the information of the item to a data orb so you can make copies of them with the replicator. This process uses a varying amount of UUM and time, depending on the item. Scanning also takes forever.\n\nSome items you might want to replicate include:\nIndium\nPlatline metals like Iridium and Osmium\nNeon, Krypton, Xenon from liquid air distilling\nOganesson\n\nFor some things (Oganesson), this might be considered the only reasonable way of getting a decent amount.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 484, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§a§lCreate New Materials Out of Pure Energy", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1879, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 7, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}, "1:10": {"Count:3": 8, "Damage:2": 11028, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "IC2:itemPartCircuitAdv"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 32707, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 482, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}}}