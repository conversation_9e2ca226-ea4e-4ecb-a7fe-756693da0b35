{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2648}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "The next material is Infinity Catalyst. Infinity Catalyst needs to be compressed with insane amounts of force. The only machine capable of doing that is a Neutronium Compressor. Check out the Neutronium Compressor quest in Multiblock Goals.\n\nTo make the Catalyst, go to Vega B to get the dust. Then use the Neutronium Compressor to make Infinity Catalyst with 64 of the dust.\n\nYou can also do the insane Avaritia recipe to make the Infinity Catalyst, if you want a challenge :P\n\n[note]The compressor can also be used to make singularities, culminating in the Eternal Singularity, used in the alternate recipe.[/note]\n\n[note]If you want to automate singularity creation you can just click with the middle mouse button to modify the number of items in a pattern beyond the normal limits.[/note]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 5, "OreDict:8": "", "id:8": "Avaritia:Resource"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Compressing the Core of a Star", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2674, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 64, "Damage:2": 2394, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 5, "OreDict:8": "", "id:8": "Avaritia:Resource"}}, "taskID:8": "bq_standard:retrieval"}}}