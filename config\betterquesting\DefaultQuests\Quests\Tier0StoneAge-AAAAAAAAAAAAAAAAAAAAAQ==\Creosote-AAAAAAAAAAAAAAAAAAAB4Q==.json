{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 24}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "I guess you already turned some wood into charcoal and wonder what to do with all this creosote? Well, you can burn it in a normal Furnace to cook your food or anything else, or you can make some torches with it. You can save up on the Coal variants, while at the same time keeping the Coke Oven running for the items you really need.\n\nYou can use a Bucket to pick up the Creosote, but not a Clay Bucket, either through the icon in the machine's interface, or by right-clicking the Coke Oven with an empty bucket in hand.\n\nYou will eventually want to automate Coke Ovens, and that's when you can use it to generate power. This is mostly LV content, but the singleblock option is the GT++ Semifluid Generator, whereas the multiblock option is the Railcraft Liquid Fueled Boiler. You'll unlock the quests for these when the time comes.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "Railcraft:fluid.creosote.bucket"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§l§2§lCreosote", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 481, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:bucket"}, "1:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "IguanaTweaksTConstruct:clayBucketFired"}}, "ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:choice"}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "Railcraft:fluid.creosote.bucket"}}, "taskID:8": "bq_standard:retrieval"}}}