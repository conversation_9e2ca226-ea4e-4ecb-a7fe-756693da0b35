{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 548}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 550}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 543, "type:1": 1}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "By adding Hydrogen to Sulfuric Light Fuel in a Chemical Reactor you can produce Light Fuel with the very high burn value of 305,000 EU per cell. It is also the main component of Diesel, an excellent fuel that becomes available in MV\n\nThe remaining Hydrogen Sulfide can be used to produce Sulfuric Acid for, well, tons of stuff. You can never have enough of it! You can also crack Light Fuel in a Chemical Reactor and then distill it to get Ethylene, for Polyethylene. But Refinery Gas is better for that.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 30740, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§5§lLight Fuel", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 549, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "irontank:obsidianTank"}, "1:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "gregtech:gt.integrated_circuit"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 3, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinChemistI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 48, "Damage:2": 30740, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}}}