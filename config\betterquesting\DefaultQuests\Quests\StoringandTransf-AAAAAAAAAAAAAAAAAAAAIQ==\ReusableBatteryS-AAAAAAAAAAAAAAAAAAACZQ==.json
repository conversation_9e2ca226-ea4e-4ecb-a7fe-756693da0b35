{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 624}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 609}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Sodium, cadmium, and lithium batteries are rechargeable batteries. They're crafted without stored energy, but can be charged in any gregtech machine. If unneeded, they can be placed in an extractor and converted back into small battery hulls, but their contents are lost.\n\nIf placed in a machine, it will fill up if there's excess power, and drain if the machine can't get enough. Handy for high-EU machines you don't want to choke.\n\n[note]Sodium is the one that stores the least EU of the 3 rechargeable batteries, but is definitely the easiest to get.[/note]", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 32519, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01", "tag:10": {"GT.ItemCharge:4": 50000}}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Reusable Battery: Sodium", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 613, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 2, "Damage:2": 2017, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 4, "Damage:2": 2315, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 4, "Damage:2": 1246, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "3:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 15, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinChemist"}, "1:10": {"Count:3": 15, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 32519, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}}}