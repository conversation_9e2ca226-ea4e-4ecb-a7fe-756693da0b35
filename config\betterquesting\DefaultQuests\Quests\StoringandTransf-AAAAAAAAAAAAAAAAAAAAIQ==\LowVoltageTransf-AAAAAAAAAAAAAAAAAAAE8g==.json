{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 103}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "It is possible to run MV machines while still in LV - build a transformer and use 4 power sources 32 eu/t to power one 128 eu/t machine.\n\nIt transforms power up or down, depending on the mode, which you can switch with a soft mallet. Step-up means 4A LV in, 1A MV out, while step-down means the reverse.\n\nMake sure you understand the following if you prefer your base not being a crater:\nA step-down mode is the default one. In this mode, the large dot is the input. In a step-up mode, it's the output. Do not mix it up. LV machines that receive MV power will explode.\n\nIf the transformer is connected to power, or was in the past, then do not change the mode with stuff connected. It's better to break and place it again to get rid of the stored power rather than rotate it with the wrench and blow things up. Make sure to also disconnect the nearby wires with a wire cutter.\n\nAs a reminder, machines don't blow up if they are given more amps than they can consume. They blow up from higher voltage tier.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 21, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Low Voltage Transformer", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1266, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 3, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 21, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}}}