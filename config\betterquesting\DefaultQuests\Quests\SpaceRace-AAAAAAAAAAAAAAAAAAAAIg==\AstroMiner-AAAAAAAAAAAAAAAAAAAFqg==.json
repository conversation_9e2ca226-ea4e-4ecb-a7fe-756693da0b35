{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1447}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "The Astro Miner is an automatic mining vehicle which moves around, makes tunnels, mines up everything it finds, and brings it all back to a base station where it docks. It is not player-rideable, so don't try and climb inside it!\n\nIt's not very fast or efficient, and it seems to discard half or more of the ore it attempts to collect. Only consider using it if you want the huge amount of planet blocks it picks up.[/note]", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "GalacticraftMars:item.itemAstroMiner"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Astro Miner", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1450, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 16, "Damage:2": 11884, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 4, "Damage:2": 6, "OreDict:8": "", "id:8": "GalacticraftMars:item.itemBasicAsteroids"}, "2:10": {"Count:3": 1, "Damage:2": 7, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSpaceII"}, "1:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 102, "OreDict:8": "", "id:8": "GalaxySpace:item.RocketControlComputer"}, "1:10": {"Count:3": 3, "Damage:2": 0, "OreDict:8": "", "id:8": "GalacticraftCore:item.heavyPlating"}, "2:10": {"Count:3": 1, "Damage:2": 3, "OreDict:8": "", "id:8": "GalacticraftMars:item.null"}, "3:10": {"Count:3": 3, "Damage:2": 0, "OreDict:8": "", "id:8": "GalacticraftMars:item.itemBasicAsteroids"}, "4:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "GalacticraftMars:item.orionDrive"}, "5:10": {"Count:3": 2, "Damage:2": 14, "OreDict:8": "", "id:8": "GalacticraftCore:item.basicItem"}, "6:10": {"Count:3": 2, "Damage:2": 32603, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "7:10": {"Count:3": 2, "Damage:2": 1, "OreDict:8": "", "id:8": "IronChest:BlockIronChest"}, "8:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "GalacticraftMars:tile.beamReceiver"}, "9:10": {"Count:3": 1, "Damage:2": 8, "OreDict:8": "", "id:8": "GalacticraftMars:item.itemBasicAsteroids"}, "10:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "GalacticraftCore:item.steelPole"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "GalacticraftMars:item.itemAstroMiner"}}, "taskID:8": "bq_standard:retrieval"}}}