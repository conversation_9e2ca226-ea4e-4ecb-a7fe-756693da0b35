{"preRequisites:9": {"0:10": {"questIDHigh:4": -8986332492408667499, "questIDLow:4": -6067382707378224204, "type:1": 1}, "1:10": {"questIDHigh:4": -6962518690474080185, "questIDLow:4": -6893323685044150631}, "2:10": {"questIDHigh:4": 6007149920671187507, "questIDLow:4": -5562896910886444976}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "As usage of the Forge continues, more data on graviton shards is gathered. It has been found that they can stabilize the outputs of the Forge, allowing for the direct extraction of molten material while it is still close to the heart of the star.\n\n[note]The second module of the Godforge, Helioflux Melting Core allows processing EBF recipes by directly outputting molten metal when possible. This may not always be as efficient as other methods, but the time and scaling saved from Vacuum Freezer steps often more than makes up for this.[/note]\n\n[note]Usage of this module requires the FDIM upgrade.[/note]\n\n[warn]Helioflux Melting Core <3[/warn]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 15413, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§b§lHelioflux Melting Core my Beloved", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 1930161895658832038, "questIDLow:4": -9020796615899025430, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 15413, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 20, "Damage:2": 0, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "1:10": {"Count:3": 20, "Damage:2": 2, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "2:10": {"Count:3": 5, "Damage:2": 8, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "3:10": {"Count:3": 5, "Damage:2": 1, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}, "4:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "tectech:gt.godforgecasing"}}, "taskID:8": "bq_standard:optional_retrieval"}}}