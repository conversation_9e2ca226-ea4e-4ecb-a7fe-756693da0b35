{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1781}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Putting redstone on my weapon makes it faster. So why don't I put it on my hands directly!\nThis will increase your mining speed.\n\nHint:\nEvery Armor part has 3 Slots for modification, aside from gloves, which have 5.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:travelGlove", "tag:10": {"TinkerAccessory:10": {"BaseDurability:4": 500, "BonusDurability:4": 0, "Broken:4": 0, "Built:4": 1, "Damage:4": 0, "Effect1:4": 1, "MiningSpeed:4": 500, "ModDurability:6": 0.0, "ModifierTip1:8": "Redstone (500/500)", "Modifiers:4": 0, "Redstone:9": {"0:4": 500, "1:4": 500, "2:4": 1}, "Tooltip1:8": "<PERSON><PERSON>", "TotalDurability:4": 500}}}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Traveller's Gloves Upgrades", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1785, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:slime.pad"}, "1:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:piston"}, "2:10": {"Count:3": 1, "Damage:2": 5, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSmith"}, "1:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSurvivor"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:travelGlove", "tag:10": {"TinkerAccessory:10": {"BaseDurability:4": 500, "BonusDurability:4": 0, "Broken:4": 0, "Built:4": 1, "Damage:4": 0, "ModDurability:6": 0.0, "Modifiers:4": 5, "TotalDurability:4": 500}}}, "1:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:redstone"}, "2:10": {"Count:3": 55, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:redstone_block"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:travelGlove", "tag:10": {"TinkerAccessory:10": {"BaseDurability:3": 500, "BonusDurability:3": 0, "Broken:1": 0, "Built:1": 1, "Damage:3": 0, "Effect1:3": 1, "MiningSpeed:3": 500, "ModDurability:5": 0.0, "ModifierTip1:8": "§4Redstone (500/500)", "Modifiers:3": 0, "Redstone:11": [500, 500, 1], "Tooltip1:8": "§4Haste", "TotalDurability:3": 500}}}}, "taskID:8": "bq_standard:retrieval"}}}