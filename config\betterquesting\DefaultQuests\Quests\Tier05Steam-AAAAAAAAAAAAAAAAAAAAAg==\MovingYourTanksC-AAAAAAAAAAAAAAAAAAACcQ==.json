{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 65}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 69, "type:1": 1}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Want to move your chests and barrels without spilling all the things (buildcraft™)? No problem! Make a dolly and you can carry your full chests, barrels or storage drawers anywhere you want. But be warned, you will get a slowness debuff so hopefully you don't have to travel very far. You can also move non-portable buildcraft and iron tanks without losing fluids, and worktables without losing stored recipes.\n\nShift-right-click an empty dolly to collapse it. It won't carry anything until you unfold it, but it will stack and can go in your backpack.\n\n§3PS: Later on you can fly or make a traveller's belt to get around the debuff.§r", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "JABBA:mover"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§3§lMoving Your Tanks, Chests, and Barrels", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 625, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "JABBA:barrel"}}}, "1:10": {"choices:9": {"0:10": {"Count:3": 3, "Damage:2": 0, "OreDict:8": "", "id:8": "JABBA:upgradeStructural"}, "1:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "JABBA:upgradeStructural"}, "2:10": {"Count:3": 5, "Damage:2": 5, "OreDict:8": "", "id:8": "StorageDrawersBop:fullDrawers1"}, "3:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:choice"}, "2:10": {"ignoreDisabled:1": 0, "index:3": 2, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 10, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 2, "Damage:2": 32100, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 2, "Damage:2": 25880, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 3, "Damage:2": 23032, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "3:10": {"Count:3": 1, "Damage:2": 17305, "OreDict:8": "plateSteel", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "JABBA:mover"}}, "taskID:8": "bq_standard:retrieval"}}}