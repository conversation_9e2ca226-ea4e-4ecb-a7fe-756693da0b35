{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 689}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 102}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 838}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "The goggles, wings, shoes, vest, belt and boots itself are not really special.\n\nYou can zoom with the googles, have an extra inventory hotbar with the belt, step-up assist with the shoes, jump boost with the wings and faster swimming ability with the vest.\n\nYou get protection like with normal armor too.\n\nLater on you can upgrade your items to become more powerful ones.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:travelVest", "tag:10": {"TinkerArmor:10": {"BaseDefense:6": 4.0, "BaseDurability:4": 1035, "BonusDurability:4": 0, "Broken:4": 0, "Built:4": 1, "Damage:4": 0, "DamageReduction:6": 0.0, "MaxDefense:6": 10.0, "ModDurability:6": 0.0, "Modifiers:4": 3, "TotalDurability:4": 1035}}}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Traveller's Gear", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1781, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 32, "Damage:2": 0, "OreDict:8": "", "id:8": "harvestcraft:hardenedleatherItem"}, "1:10": {"Count:3": 16, "Damage:2": 17500, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "IC2:itemPartCircuitAdv"}, "3:10": {"Count:3": 4, "Damage:2": 5, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSmithI"}, "1:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSurvivorI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 2, "Damage:2": 24500, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 6, "Damage:2": 17500, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "2:10": {"Count:3": 2, "Damage:2": 28303, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "3:10": {"Count:3": 1, "Damage:2": 26303, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "4:10": {"Count:3": 17, "Damage:2": 0, "OreDict:8": "", "id:8": "harvestcraft:hardenedleatherItem"}, "5:10": {"Count:3": 3, "Damage:2": 17804, "OreDict:8": "plateObsidian", "id:8": "gregtech:gt.metaitem.01"}, "6:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:diamond_helmet"}, "7:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:diamond_chestplate"}, "8:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:diamond_leggings"}, "9:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:diamond_boots"}, "10:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:fletching"}, "11:10": {"Count:3": 2, "Damage:2": 32642, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "12:10": {"Count:3": 6, "Damage:2": 0, "OreDict:8": "circuitAdvanced", "id:8": "IC2:itemPartCircuitAdv"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:travelGoggles", "tag:10": {"TinkerArmor:10": {"BaseDefense:6": 0.0, "BaseDurability:4": 1035, "BonusDurability:4": 0, "Broken:4": 0, "Built:4": 1, "Damage:4": 0, "DamageReduction:6": 0.0, "MaxDefense:6": 4.0, "ModDurability:6": 0.0, "Modifiers:4": 3, "TotalDurability:4": 1035}}}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:travelVest", "tag:10": {"TinkerArmor:10": {"BaseDefense:6": 4.0, "BaseDurability:4": 1035, "BonusDurability:4": 0, "Broken:4": 0, "Built:4": 1, "Damage:4": 0, "DamageReduction:6": 0.0, "MaxDefense:6": 10.0, "ModDurability:6": 0.0, "Modifiers:4": 3, "TotalDurability:4": 1035}}}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:travelWings", "tag:10": {"TinkerArmor:10": {"BaseDefense:6": 2.0, "BaseDurability:4": 1035, "BonusDurability:4": 0, "Broken:4": 0, "Built:4": 1, "Damage:4": 0, "DamageReduction:6": 0.0, "MaxDefense:6": 8.0, "ModDurability:6": 0.0, "Modifiers:4": 3, "TotalDurability:4": 1035}}}, "3:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:travelBelt", "tag:10": {"TinkerAccessory:10": {"BaseDurability:4": 500, "BonusDurability:4": 0, "Broken:4": 0, "Built:4": 1, "Damage:4": 0, "ModDurability:6": 0.0, "Modifiers:4": 5, "TotalDurability:4": 500}}}, "4:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:travelGlove", "tag:10": {"TinkerAccessory:10": {"BaseDurability:4": 500, "BonusDurability:4": 0, "Broken:4": 0, "Built:4": 1, "Damage:4": 0, "ModDurability:6": 0.0, "Modifiers:4": 5, "TotalDurability:4": 500}}}, "5:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:travelBoots", "tag:10": {"TinkerArmor:10": {"BaseDefense:6": 2.0, "BaseDurability:4": 1035, "BonusDurability:4": 0, "Broken:4": 0, "Built:4": 1, "Damage:4": 0, "DamageReduction:6": 0.0, "MaxDefense:6": 6.0, "ModDurability:6": 0.0, "Modifiers:4": 3, "TotalDurability:4": 1035}}}}, "taskID:8": "bq_standard:retrieval"}}}