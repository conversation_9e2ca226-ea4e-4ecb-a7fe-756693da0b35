{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2599}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "For a while now, you have been able to get away with cutting and engraving your silicon circuit wafers with nothing but the bare metal of your machines. However, this can cause significant imperfections in the final wafer, so now that you are producing even more advanced circuitry such imperfections are no longer tolerable.\n\nWith the ability to make LuV components, you can now make the Water Purification Plant. This multiblock allows you to make purified water, which will be helpful in making wafers of increasing purity and perfection. For your current Naquadah-Doped Wafers, this is not yet necessary, but using purified water may significantly speed up the process, so it's definitely worth checking out.\n\nThe Water Purification Plant does not run recipes itself. Instead, it provides the core logistics for your water purification line to work. Around it you can build water purification units that link to the Water Purification Plant. The plant then provides power and maintenance to the different units, and synchronizes their recipe cycles.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 9402, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§c§lIndustrial Water Purification", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": -330443813993036947, "questIDLow:4": -6215508413557630456, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 41, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag", "tag:10": {"ench:9": {"0:10": {"id:2": 35, "lvl:2": 3}}}}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 9402, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 77, "Damage:2": 4, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings9"}, "1:10": {"Count:3": 71, "Damage:2": 5, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings9"}, "2:10": {"Count:3": 56, "Damage:2": 3, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings9"}, "3:10": {"Count:3": 30, "Damage:2": 81, "OreDict:8": "", "id:8": "gregtech:gt.blockframes"}}, "taskID:8": "bq_standard:optional_retrieval"}}}