{"id": "dyson_swarm", "condition": {"type": "achievement", "id": "dyson_swarm"}, "model": {"type": "complex", "keys": {"b": "gtnhintergalactic:dysonswarmparts", "c": "gregtech:gt.blockcasings5", "d": "gtnhintergalactic:dysonswarmparts", "e": "gtnhintergalactic:dysonswarmparts", "f": "gregtech:gt.blockmachines", "g": "gregtech:gt.blockmachines", "h": "gregtech:gt.blockcasings6", "i": "gtnhintergalactic:dysonswarmparts", "j": "gtnhintergalactic:dysonswarmparts", "k": "gregtech:gt.blockmachines", "m": "gtnhintergalactic:dysonswarmparts", "n": "gtnhintergalactic:dysonswarmparts", "o": "gtnhintergalactic:dysonswarmparts", "p": "gtnhintergalactic:dysonswarmparts", "s": "gtnhintergalactic:dysonswarmparts", "t": "gtnhintergalactic:dysonswarmparts", "x": "gtnhintergalactic:dysonswarmparts", "y": "gtnhintergalactic:dysonswarmparts", "z": "gtnhintergalactic:dysonswarmparts", "~": "gregtech:gt.blockmachines"}, "metadata": {"b": 9, "c": 8, "d": 1, "e": 0, "f": 4470, "g": 4124, "h": 10, "i": 2, "j": 3, "k": 5081, "m": 4, "n": 9, "o": 5, "p": 6, "s": 7, "t": 8, "x": 0, "y": 5, "z": 2, "~": 14001}, "transpose": true, "structure": [["                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "  ttt           ", "  ttt        k  ", "  ttt       k k ", "             k  ", "                "], ["                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "  ttt           ", " ttttt          ", " ttttt       k  ", " ttttt      k k ", "  ttt        k  ", "                "], ["                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "  ttt           ", " ttttt          ", " ttttt       k  ", " ttttt      k k ", "  ttt        k  ", "                "], ["                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "  ttt           ", " ttttt          ", " ttttt       k  ", " ttttt      k k ", "  ttt        k  ", "                "], ["                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "  ttt           ", "  tst        k  ", "  ttt       k k ", "             k  ", "                "], ["                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "                ", "   s         k  ", "            k k ", "             k  ", "                "], ["                ", "                ", "                ", "                ", "                ", "          h     ", "                ", "                ", "                ", "                ", "  ttt           ", " t g t          ", " tgsgt       k  ", " t g t      k k ", "  ttt        k  ", "                "], ["                ", "                ", "                ", "                ", "                ", "          f     ", "                ", "                ", "                ", "                ", "                ", "                ", "   s         k  ", "            k k ", "             k  ", "                "], ["         ddd    ", "        d   d   ", "       d     d  ", "      d       d ", "     d         d", "     d    f    d", "     d         d", "      d       d ", "       d     d  ", "        d   d   ", "  ttt    ddd    ", " t g t          ", " tgsgt      kmk ", " t g t      m m ", "  ttt       kmk ", "                "], ["                ", "         ddd    ", "        ddddd   ", "       dd   dd  ", "      dd     dd ", "      dd  f  dd ", "      dd     dd ", "       dd   dd  ", "        ddddd   ", "         ddd    ", "                ", "                ", "   s        kmk ", "            m m ", "            kmk ", "                "], ["                ", "                ", "                ", "         ddd    ", "        ddddd   ", "        ddddd   ", "        ddddd   ", "         ddd    ", "                ", "                ", "  ttt           ", " t g t          ", " tgsgt      kmk ", " t g t      m m ", "  ttt       kmk ", "                "], ["                ", "                ", "                ", "                ", "         f f    ", "                ", "         f f    ", "                ", "                ", "                ", "                ", "                ", "   s        kmk ", "            m m ", "            kmk ", "                "], ["                ", "                ", "                ", "                ", "         f f    ", "                ", "         f f    ", "                ", "                ", "                ", "  ttt           ", " t g t          ", " tgsgt      kmk ", " t g t      m m ", "  ttt       kmk ", "                "], ["                ", "                ", "                ", "                ", "         f f    ", "                ", "         f f    ", "                ", "                ", "                ", "                ", "                ", "   s        kmk ", "            m m ", "            kmk ", "                "], ["                ", "                ", "                ", "                ", "         f f    ", "                ", "         f f    ", "                ", "                ", "                ", "                ", "                ", "   s        kmk ", "            m m ", "            kmk ", "                "], ["                ", "                ", "                ", "                ", "         f f    ", "                ", "         f f    ", "                ", "                ", "                ", "  ppp           ", " p   p          ", " p s p      kmk ", " p   p      m m ", "  ppp       kmk ", "                "], ["                ", "                ", "                ", "        xxxxx   ", "        xxxxx   ", "        xxxxx   ", "        xxxxx   ", "        xxxxx   ", "                ", "yyyyyyy         ", "yyyyyyy         ", "yypppyy    zzzzz", "yypypyy    zzzzz", "yypppyy    zzjzz", "yyyyyyy    zzzzz", "yyyyyyy    zzzzz"], ["                ", "                ", "                ", "        xeeex   ", "        eccce   ", "        eccce   ", "        eccce   ", "        xeeex   ", "                ", "ooooooo         ", "oyyyyyo         ", "oyyyyyo    ziiiz", "oyyyyyo    izzzi", "oyyyyyo    izzzi", "oyyyyyo    izzzi", "ooooooo    ziiiz"], ["                ", "                ", "                ", "        xx~xx   ", "        xxxxx   ", "        xxxxx   ", "        xxxxx   ", "        xxxxx   ", "                ", "yyyyyyy         ", "yyyyyyy         ", "yyyyyyy    zzzzz", "yyyyyyy    zzzzz", "yyyyyyy    zzzzz", "yyyyyyy    zzzzz", "yyyyyyy    zzzzz"], ["bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbnbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb", "bbbbbbbbbbbbbbbb"]]}}