{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2007}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "The QC has 2-24 computer racks depending on the length of the machine. Each rack has four total slots for either circuits (to generate heat and computation) or vents (to dissipate heat). Although conceptually similar to a nuclear reactor, the location of the components inside does not matter at all. Every rack is also completely independent from every other rack which means it is not possible to have one rack with all circuits and another with all vents.\n\nThe computation of a rack is the sum of all the components' individual computation. For example, two APU (Tier 3) and two Cooling Cores have 240 + 240 + 0 + 0 = 480 base computation per second. However, computation changes significantly depending on the values for overclock and overvolt:\n\n[note]Computation = Base * (1+overclock^2) / (1+(overclock-overvolt)^2)[/note]\n\nThe heat of a rack is a value between 0 and 10,000 and is reported in the interface of the QC or by using a Portable Scanner on the rack. Every component has a certain heat limit which if exceeded will void that component. Breaking or wrenching a rack while it is hot (>2000) will similarly void the components inside. The QC will explode if the heat exceeds 10,000 but no component has a heat limit greater than that, so don't worry too much about ruining your base.\n\n[note]Heat α HeatConstant * overclock * overvolt^2[/note]\n\nThe power consumption of a rack is 1A ZPM regardless of the components inside. This is in addition to the 1A ZPM of the QC itself. However, these EU/t values are directly proportional to overclock and overvolt which should increase as you unlock better circuits. Be sure to turn on the QC before the research station and only when you need it.\n\n[note]EU/t = 131,072 * overclock * overvolt * (racks + 1)[/note]\n\nTo get you started, craft 4x APU (Tier 3) and 4x Cooling Cores. Place two of each component in the two racks and the QC will safely produce 1,920 computation/s for just 393,215 EU/t (0.75A UV) which is more than enough for researching the Wetware Mainframe. If you would like to further increase the computation/s at the cost of power, adjust the overclock and overvolt values to 1.44 and 1.03, respectively. This will generate 2,526 computation/s at a cost of 583,217 EU/t (1.11A UV).\n\nIf the APUs are too bothersome to craft, try Crystal Mainframes instead but you will need different overclock and overvolt values to get the most out of them. That goes for every unique combination of circuits and vents.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 15450, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Quantum Mechanics is Hot", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2699, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 4, "Damage:2": 103, "OreDict:8": "", "id:8": "OpenComputers:item"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 4, "Damage:2": 2, "OreDict:8": "", "id:8": "GraviSuite:itemSimpleItem"}}, "taskID:8": "bq_standard:retrieval"}}}