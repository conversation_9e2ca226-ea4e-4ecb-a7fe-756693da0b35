<?xml version="1.0" encoding="utf-8" ?>
<!--
|==============================================================|
| INVENTORY TWEAKS Mod - By <PERSON><PERSON> (jimeo.wan at gmail.com) |
| Tree configuration                                           |
|==============================================================|

# Q: WHAT IS THIS?
# A: This file is used to store all the keywords you can use to create item rules, but also to define the items order.
# You can move/change all that you want, but it's recommended not to delete any default item.

# Q: MY CUSTOM FILE WAS REPLACED =(
# A: Since a new major version of Minecraft appeared, I silently updated the tree... BUT!
# I kept a backup of your version in the same directory. Sorry for the trouble, feel free to import your changes in the new file.

# Q: THE FILE IS BROKEN AND I DON'T KNOW WHAT TO DO =(
# A: Just delete this file, the default tree will be reloaded next time you sort something!

# FULL DOCUMENTATION
# http://modding.kalam-alami.net/invtweaks

# CREDITS
# Thanks to Larandar & Lunaqua for their contributions.
-->
<!-- Note: 'stuff' matches everything, even items that don't appear here -->
<stuff treeVersion="1.7.0">
    <equipment>
        <weapon>
            <sword>
                <ironSword id="iron_sword"/>
                <diamondSword id="diamond_sword"/>
                <stoneSword id="stone_sword"/>
                <goldSword id="golden_sword"/>
                <woodSword id="wooden_sword"/>
            </sword>
            <munition>
                <bow id="bow"/>
                <arrow id="arrow"/>
                <snowball id="snowball"/>
            </munition>
        </weapon>
        <tool>
            <pickaxe>
                <diamondPickaxe id="diamond_pickaxe"/>
                <goldPickaxe id="golden_pickaxe"/>
                <ironPickaxe id="iron_pickaxe"/>
                <stonePickaxe id="stone_pickaxe"/>
                <woodPickaxe id="wooden_pickaxe"/>
            </pickaxe>
            <shovel>
                <goldShovel id="golden_shovel"/>
                <diamondShovel id="diamond_shovel"/>
                <ironShovel id="iron_shovel"/>
                <stoneShovel id="stone_shovel"/>
                <woodShovel id="wooden_shovel"/>
            </shovel>
            <axe>
                <goldAxe id="golden_axe"/>
                <diamondAxe id="diamond_axe"/>
                <ironAxe id="iron_axe"/>
                <stoneAxe id="stone_axe"/>
                <woodAxe id="wooden_axe"/>
            </axe>
            <hoe>
                <goldHoe id="golden_hoe"/>
                <diamondHoe id="diamond_hoe"/>
                <ironHoe id="stone_hoe"/>
                <stoneHoe id="iron_hoe"/>
                <woodHoe id="wooden_hoe"/>
            </hoe>
            <shears id="shears"/>
            <fishingRod id="fishing_rod"/>
            <carrotOnAStick id="carrot_on_a_stick"/>
        </tool>
        <armor>
            <helmet>
                <diamondHelmet id="diamond_helmet"/>
                <chainHelmet id="chainmail_helmet"/>
                <ironHelmet id="iron_helmet"/>
                <goldHelmet id="golden_helmet"/>
                <leatherHelmet id="leather_helmet"/>
            </helmet>
            <chestplate>
                <diamondChestplate id="diamond_chestplate"/>
                <chainChestplate id="chainmail_chestplate"/>
                <ironChestplate id="iron_chestplate"/>
                <goldChestplate id="golden_chestplate"/>
                <leatherChestplate id="leather_chestplate"/>
            </chestplate>
            <leggings>
                <diamondLeggings id="diamond_leggings"/>
                <chainLeggings id="chainmail_leggings"/>
                <ironLeggings id="iron_leggings"/>
                <goldLeggings id="golden_leggings"/>
                <leatherLeggings id="leather_leggings"/>
            </leggings>
            <boots>
                <diamondBoots id="diamond_boots"/>
                <chainBoots id="chainmail_boots"/>
                <ironBoots id="iron_boots"/>
                <goldBoots id="golden_boots"/>
                <leatherBoots id="leather_boots"/>
            </boots>
        </armor>
    </equipment>
    <block>
        <naturalBlock>
            <stones>
                <stone id="stone"/>
                <cobblestone id="cobblestone"/>
                <mossStone id="mossy_cobblestone"/>
                <endStone id="end_stone"/>
                <whiteStone id="end_stone"/>
            </stones>
            <oreBlock>
                <coalOreBlock id="coal_ore"/>
                <redstoneOreBlock>
                    <unlitRedstoneOreBlock id="redstone_ore"/>
                    <litRedstoneOreBlock id="lit_redstone_ore"/>
                </redstoneOreBlock>
                <lapisLazuliOreBlock id="lapis_ore"/>
                <diamondOreBlock id="diamond_ore"/>
                <emeraldOreBlock id="emerald_ore"/>
            </oreBlock>
            <dirts>
                <dirt id="dirt" damage="0"/>
                <grasslessDirt id="dirt" damage="1"/>
                <podzol id="dirt" damage="2"/>
            </dirts>
            <wood>
                <oakWood id="log" damage="0"/>
                <birchWood id="log" damage="1"/>
                <spruceWood id="log" damage="2"/>
                <jungleWood id="log" damage="3"/>
                <acaciaWood id="log2" damage="0"/>
                <darkOakWood id="log2" damage="1"/>
                <woodDict oreDictName="logWood"/>
            </wood>
            <sands>
                <sand id="sand">
                    <plainSand id="sand" damage="0"/>
                    <redSand id="sand" damage="1"/>
                </sand>
                <sandstone>
                    <classicSandstone id="sandstone" damage="0"/>
                    <creeperSandstone id="sandstone" damage="1"/>
                    <smoothSandstone id="sandstone" damage="2"/>
                </sandstone>
            </sands>
            <gravel id="gravel"/>
            <clayBlock id="clay"/>
            <netherBlock>
                <glowstoneBlock id="glowstone"/>
                <netherrack id="netherrack"/>
                <soulSand id="soul_sand"/>
                <quartzOreBlock id="quartz_ore"/>
            </netherBlock>
            <web id="web"/>
            <cobweb id="web"/>
            <monsterSpawner id="mob_spawner"/>
            <bedrock id="bedrock"/>
            <sponge id="sponge"/>
            <grass id="grass"/>
            <mycelium id="mycelium"/>
            <mushroomGrass id="mycelium"/>
            <farmland id="farmland"/>
            <snow id="snow"/>
            <ice id="ice"/>
            <packedIce id="packed_ice"/>
            <liquid>
                <water id="flowing_water"/>
                <water id="water"/>
                <lava id="flowing_lava"/>
                <lava id="lava"/>
            </liquid>
            <portal id="portal"/>
            <airPortal id="end_portal_frame"/>
            <endPortal id="end_portal"/>
            <endPortalFrame id="end_portal_frame"/>
            <air id="air"/>
            <fire id="fire"/>
            <obsidian id="obsidian"/>
            <mushroomBlock>
                <hugeBrownMushroom id="brown_mushroom_block"/>
                <hugeRedMushroom id="red_mushroom_block"/>
            </mushroomBlock>
            <dragonEgg id="dragon_egg"/>
            <hiddenSilverfish id="monster_egg"/>
        </naturalBlock>
        <wool>
            <whiteWool id="wool" damage="0"/>
            <orangeWool id="wool" damage="1"/>
            <magentaWool id="wool" damage="2"/>
            <lightBlueWool id="wool" damage="3"/>
            <yellowWool id="wool" damage="4"/>
            <lightGreenWool id="wool" damage="5"/>
            <limeWool id="wool" damage="5"/>
            <pinkWool id="wool" damage="6"/>
            <grayWool id="wool" damage="7"/>
            <lightGrayWool id="wool" damage="8"/>
            <cyanWool id="wool" damage="9"/>
            <purpleWool id="wool" damage="10"/>
            <blueWool id="wool" damage="11"/>
            <brownWool id="wool" damage="12"/>
            <greenWool id="wool" damage="13"/>
            <darkGreenWool id="wool" damage="13"/>
            <redWool id="wool" damage="14"/>
            <blackWool id="wool" damage="15"/>
        </wool>
        <manufacturedBlock>
            <ladder id="ladder"/>
            <fence>
                <woodenFence id="fence"/>
                <netherFence id="nether_brick_fence"/>
                <netherBrickFence id="nether_brick_fence"/>
                <fenceGate id="fence_gate"/>
            </fence>
            <carpet>
                <whiteCarpet id="carpet" damage="0"/>
                <orangeCarpet id="carpet" damage="1"/>
                <magentaCarpet id="carpet" damage="2"/>
                <lightBlueCarpet id="carpet" damage="3"/>
                <yellowCarpet id="carpet" damage="4"/>
                <lightGreenCarpet id="carpet" damage="5"/>
                <limeCarpet id="carpet" damage="5"/>
                <pinkCarpet id="carpet" damage="6"/>
                <grayCarpet id="carpet" damage="7"/>
                <lightGrayCarpet id="carpet" damage="8"/>
                <cyanCarpet id="carpet" damage="9"/>
                <purpleCarpet id="carpet" damage="10"/>
                <blueCarpet id="carpet" damage="11"/>
                <brownCarpet id="carpet" damage="12"/>
                <greenCarpet id="carpet" damage="13"/>
                <darkGreenCarpet id="carpet" damage="13"/>
                <redCarpet id="carpet" damage="14"/>
                <blackCarpet id="carpet" damage="15"/>
            </carpet>
            <stoneWall>
                <cobblestoneWall id="cobblestone_wall" damage="0"/>
                <mossyCobblestoneWall id="cobblestone_wall" damage="1"/>
            </stoneWall>
            <sign id="standing_sign"/>
            <jackOLantern id="lit_pumpkin"/>
            <constructionBlock>
                <woodenPlank>
                    <oakPlank id="planks" damage="0"/>
                    <sprucePlank id="planks" damage="1"/>
                    <birchPlank id="planks" damage="2"/>
                    <junglePlank id="planks" damage="3"/>
                    <acaciaPlank id="planks" damage="4"/>
                    <darkOakPlank id="planks" damage="5"/>
                    <woodenPlankDict oreDictName="plankWood"/>
                </woodenPlank>
                <bricks>
                    <brick id="brick_block"/>
                    <brickBlock id="brick_block"/>
                    <stoneBricks>
                        <normalStoneBrick id="stonebrick" damage="0"/>
                        <mossyStoneBrick id="stonebrick" damage="1"/>
                        <crackedStoneBrick id="stonebrick" damage="2"/>
                        <circleStoneBrick id="stonebrick" damage="3"/>
                        <chiseledStoneBrick id="stonebrick" damage="3"/>
                    </stoneBricks>
                    <netherBrick id="nether_brick"/>
                </bricks>
                <hardenedClay id="hardened_clay"/>
                <stainedClay>
                    <whiteStainedClay id="stained_hardened_clay" damage="0"/>
                    <orangeStainedClay id="stained_hardened_clay" damage="1"/>
                    <magentaStainedClay id="stained_hardened_clay" damage="2"/>
                    <lightBlueStainedClay id="stained_hardened_clay" damage="3"/>
                    <yellowStainedClay id="stained_hardened_clay" damage="4"/>
                    <lightGreenStainedClay id="stained_hardened_clay" damage="5"/>
                    <limeStainedClay id="stained_hardened_clay" damage="5"/>
                    <pinkStainedClay id="stained_hardened_clay" damage="6"/>
                    <grayStainedClay id="stained_hardened_clay" damage="7"/>
                    <lightGrayStainedClay id="stained_hardened_clay" damage="8"/>
                    <cyanStainedClay id="stained_hardened_clay" damage="9"/>
                    <purpleStainedClay id="stained_hardened_clay" damage="10"/>
                    <blueStainedClay id="stained_hardened_clay" damage="11"/>
                    <brownStainedClay id="stained_hardened_clay" damage="12"/>
                    <greenStainedClay id="stained_hardened_clay" damage="13"/>
                    <darkGreenStainedClay id="stained_hardened_clay" damage="13"/>
                    <redStainedClay id="stained_hardened_clay" damage="14"/>
                    <blackStainedClay id="stained_hardened_clay" damage="15"/>
                </stainedClay>
                <glass>
                    <glassBlock id="glass"/>
                    <glassPane id="glass_pane"/>
                </glass>
                <stainedGlass>
                    <stainedGlassBlock id="stained_glass">
                        <whiteStainedGlassBlock id="stained_glass" damage="0"/>
                        <orangeStainedGlassBlock id="stained_glass" damage="1"/>
                        <magentaStainedGlassBlock id="stained_glass" damage="2"/>
                        <lightBlueStainedGlassBlock id="stained_glass" damage="3"/>
                        <yellowStainedGlassBlock id="stained_glass" damage="4"/>
                        <lightGreenStainedGlassBlock id="stained_glass" damage="5"/>
                        <limeStainedGlassBlock id="stained_glass" damage="5"/>
                        <pinkStainedGlassBlock id="stained_glass" damage="6"/>
                        <grayStainedGlassBlock id="stained_glass" damage="7"/>
                        <lightGrayStainedGlassBlock id="stained_glass" damage="8"/>
                        <cyanStainedGlassBlock id="stained_glass" damage="9"/>
                        <purpleStainedGlassBlock id="stained_glass" damage="10"/>
                        <blueStainedGlassBlock id="stained_glass" damage="11"/>
                        <brownStainedGlassBlock id="stained_glass" damage="12"/>
                        <greenStainedGlassBlock id="stained_glass" damage="13"/>
                        <darkGreenStainedGlassBlock id="stained_glass" damage="13"/>
                        <redStainedGlassBlock id="stained_glass" damage="14"/>
                        <blackStainedGlassBlock id="stained_glass" damage="15"/>
                    </stainedGlassBlock>
                    <stainedGlassPane id="stained_glass_pane">
                        <whiteStainedGlassPane id="stained_glass_pane" damage="0"/>
                        <orangeStainedGlassPane id="stained_glass_pane" damage="1"/>
                        <magentaStainedGlassPane id="stained_glass_pane" damage="2"/>
                        <lightBlueStainedGlassPane id="stained_glass_pane" damage="3"/>
                        <yellowStainedGlassPane id="stained_glass_pane" damage="4"/>
                        <lightGreenStainedGlassPane id="stained_glass_pane" damage="5"/>
                        <limeStainedGlassPane id="stained_glass_pane" damage="5"/>
                        <pinkStainedGlassPane id="stained_glass_pane" damage="6"/>
                        <grayStainedGlassPane id="stained_glass_pane" damage="7"/>
                        <lightGrayStainedGlassPane id="stained_glass_pane" damage="8"/>
                        <cyanStainedGlassPane id="stained_glass_pane" damage="9"/>
                        <purpleStainedGlassPane id="stained_glass_pane" damage="10"/>
                        <blueStainedGlassPane id="stained_glass_pane" damage="11"/>
                        <brownStainedGlassPane id="stained_glass_pane" damage="12"/>
                        <greenStainedGlassPane id="stained_glass_pane" damage="13"/>
                        <darkGreenStainedGlassPane id="stained_glass_pane" damage="13"/>
                        <redStainedGlassPane id="stained_glass_pane" damage="14"/>
                        <blackStainedGlassPane id="stained_glass_pane" damage="15"/>
                    </stainedGlassPane>
                </stainedGlass>
                <ironBars id="iron_bars"/>
                <stairs>
                    <woodenStairs>
                        <oakWoodStairs id="oak_stairs"/>
                        <spruceWoodStairs id="spruce_stairs"/>
                        <birchWoodStairs id="birch_stairs"/>
                        <jungleWoodStairs id="jungle_stairs"/>
                        <acaciaWoodStairs id="acacia_stairs"/>
                        <darkOakWoodStairs id="dark_oak_stairs"/>
                        <woodenStairsDict oreDictName="stairWood"/>
                    </woodenStairs>
                    <cobblestoneStairs id="stone_stairs"/>
                    <stoneStairs id="stone_brick_stairs"/>
                    <brickStairs id="brick_stairs"/>
                    <netherStairs id="nether_brick_stairs"/>
                    <netherBrickStairs id="nether_brick_stairs"/>
                    <sandstoneStairs id="sandstone_stairs"/>
                    <quartzStairs id="quartz_stairs"/>
                </stairs>
                <slabs>
                    <woodenSlabs>
                        <woodenStoneSlab id="stone_slab" damage="2"/>
                        <oakWoodSlab id="wooden_slab" damage="0"/>
                        <spruceWoodSlab id="wooden_slab" damage="1"/>
                        <birchWoodSlab id="wooden_slab" damage="2"/>
                        <jungleWoodSlab id="wooden_slab" damage="3"/>
                        <woodenSlabsDict oreDictName="slabWood"/>
                    </woodenSlabs>
                    <stoneSlab id="stone_slab" damage="0"/>
                    <sandstoneSlab id="stone_slab" damage="1"/>
                    <cobblestoneSlab id="stone_slab" damage="3"/>
                    <brickSlab id="stone_slab" damage="4"/>
                    <stoneBrickSlab id="stone_slab" damage="5"/>
                    <netherBrickSlab id="stone_slab" damage="6"/>
                    <quartzSlab id="stone_slab" damage="7"/>
                    <doubleSlab>
                        <woodenDoubleSlabs>
                            <woodenStoneDoubleSlab id="double_stone_slab" damage="2"/>
                            <oakWoodDoubleSlab id="double_wooden_slab" damage="0"/>
                            <spruceWoodDoubleSlab id="double_wooden_slab" damage="1"/>
                            <birchWoodDoubleSlab id="double_wooden_slab" damage="2"/>
                            <jungleWoodDoubleSlab id="double_wooden_slab" damage="3"/>
                        </woodenDoubleSlabs>
                        <stoneDoubleSlab id="double_stone_slab" damage="0"/>
                        <sandstoneDoubleSlab id="double_stone_slab" damage="1"/>
                        <cobblestoneDoubleSlab id="double_stone_slab" damage="3"/>
                        <brickDoubleSlab id="double_stone_slab" damage="4"/>
                        <stoneBrickDoubleSlab id="double_stone_slab" damage="5"/>
                        <netherBrickDoubleSlab id="double_stone_slab" damage="6"/>
                        <quartzDoubleSlab id="double_stone_slab" damage="7"/>
                        <smoothStoneDoubleSlab id="double_stone_slab" damage="8"/>
                        <smoothSandstoneDoubleSlab id="double_stone_slab" damage="9"/>
                        <quartzTileDoubleSlab id="double_stone_slab" damage="10"/>
                    </doubleSlab>
                </slabs>
                <oreBlock>
                    <coalBlock id="coal_block"/>
                    <ironBlock id="iron_block"/>
                    <goldBlock id="gold_block"/>
                    <redstoneBlock id="redstone_block"/>
                    <lapisLazuliBlock id="lapis_block"/>
                    <diamondBlock id="diamond_block"/>
                    <emeraldBlock id="emerald_block"/>
                    <quartzBlock id="quartz_block"/>
                </oreBlock>
                <hayBale id="hay_block"/>
                <stick id="stick"/>
                <stickDict oreDictName="stickWood"/>
            </constructionBlock>
            <itemBlock>
                <cauldronBlock id="cauldron"/>
                <brewingStandBlock id="brewing_stand"/>
            </itemBlock>
        </manufacturedBlock>
        <mechanism>
            <rails>
                <simpleRails id="rail"/>
                <poweredRails id="golden_rail"/>
                <detectorRails id="detector_rail"/>
                <activatorRails id="activator_rail"/>
            </rails>
            <lighting>
                <torch id="torch"/>
                <lamp id="redstone_lamp"/>
                <redstoneLamp id="redstone_lamp"/>
                <lamp id="lit_redstone_lamp"/>
                <redstoneLamp id="lit_redstone_lamp"/>
            </lighting>
            <pistons>
                <piston id="piston"/>
                <stickyPiston id="sticky_piston"/>
                <pistonHead id="piston_head"/>
            </pistons>
            <lever id="lever"/>
            <pressurePlates>
                <pressurePlateWood id="wooden_pressure_plate"/>
                <pressurePlateStone id="stone_pressure_plate"/>
                <weightedPressurePlates>
                    <weightedPressurePlateLight id="light_weighted_pressure_plate"/>
                    <weightedPressurePlateHeavy id="heavy_weighted_pressure_plate"/>
                </weightedPressurePlates>
            </pressurePlates>
            <buttons>
                <stoneButton id="stone_button"/>
                <woodenButton id="wooden_button"/>
            </buttons>
            <doors>
                <doorWood id="wooden_door"/>
                <woodenDoor id="wooden_door"/>
                <doorIron id="iron_door"/>
                <ironDoor id="iron_door"/>
                <trapdoor id="trapdoor"/>
                <woodenDoorBlock id="wooden_door"/>
                <ironDoorBlock id="wooden_door"/>
            </doors>
            <redstoneWire id="redstone_wire"/>
            <redstoneTorch id="unlit_redstone_torch"/>
            <redstoneTorch id="redstone_torch"/>
            <redstoneRepeater id="repeater"/>
            <redstoneRepeater id="unpowered_repeater"/>
            <redstoneRepeater id="powered_repeater"/>
            <dispenser id="dispenser"/>
            <noteBlock id="noteblock"/>
            <commandBlock id="command_block"/>
            <daylightSensor id="daylight_detector"/>
            <redstoneBlock id="redstone_block"/>
            <hopper id="hopper"/>
            <dropper id="dropper"/>
            <redstoneComparator id="comparator"/>
            <redstoneComparator id="unpowered_comparator"/>
            <redstoneComparator id="powered_comparator"/>
        </mechanism>
    </block>
    <resources>
        <miningResources>
            <goldOreBlock id="gold_ore"/>
            <goldOreBlockDict oreDictName="oreGold"/>
            <silverOreBlockDict oreDictName="oreSilver"/>
            <leadOreBlockDict oreDictName="oreLead"/>
            <ironOreBlock id="iron_ore"/>
            <ironOreBlockDict oreDictName="oreIron"/>
            <copperOreBlockDict oreDictName="oreCopper"/>
            <tinOreBlockDict oreDictName="oreTin"/>
            <goldDustDict oreDictName="dustGold"/>
            <silverDustDict oreDictName="dustSilver"/>
            <leadDustDict oreDictName="dustLead"/>
            <bronzeDustDict oreDictName="dustBronze"/>
            <brassDustDict oreDictName="dustBrass"/>
            <ironDustDict oreDictName="dustIron"/>
            <tinDustDict oreDictName="dustTin"/>
            <copperDustDict oreDictName="dustCopper"/>
            <coal id="coal" damage="0"/>
            <charcoal id="coal" damage="1"/>
            <gold id="gold_ingot"/>
            <goldDict oreDictName="ingotGold"/>
            <silverDict oreDictName="ingotSilver"/>
            <leadDict oreDictName="ingotLead"/>
            <bronzeDict oreDictName="ingotBronze"/>
            <brassDict oreDictName="ingotBrass"/>
            <iron id="iron_ingot"/>
            <ironDict oreDictName="ingotIron"/>
            <tinDict oreDictName="ingotTin"/>
            <copperDict oreDictName="ingotCopper"/>
            <diamond id="diamond"/>
            <emerald id="emerald"/>
            <redstone id="redstone"/>
            <glowstoneDust id="glowstone_dust"/>
            <clayBrick id="brick"/>
            <clay id="clay_ball"/>
            <flint id="flint"/>
            <netherQuartz id="quartz"/>
        </miningResources>
        <huntingResources>
            <egg id="egg"/>
            <bone id="bone"/>
            <leather id="leather"/>
            <string id="string"/>
            <feather id="feather"/>
            <gunpowder id="gunpowder"/>
            <slimeball id="slime_ball"/>
            <enderPearl id="ender_pearl"/>
            <blazeRod id="blaze_rod"/>
            <ghastTear id="ghast_tear"/>
            <goldNugget id="gold_nugget"/>
            <netherStar id="nether_star"/>
        </huntingResources>
        <farmingResources>
            <milk id="milk_bucket"/>
            <wheat id="wheat"/>
            <paper id="paper"/>
            <book id="book"/>
            <sugarCane id="reeds"/>
        </farmingResources>
        <dye>
            <boneMeal id="dye" damage="15"/>
            <whiteDye oreDictName="dyeWhite"/>
            <inkSac id="dye" damage="0"/>
            <blackDye oreDictName="dyeBlack"/>
            <roseRed id="dye" damage="1"/>
            <redDye oreDictName="dyeRed"/>
            <cactusGreen id="dye" damage="2"/>
            <greenDye oreDictName="dyeGreen"/>
            <cocoaBeans id="dye" damage="3"/>
            <brownDye oreDictName="dyeBrown"/>
            <lapisLazuli id="dye" damage="4"/>
            <lapisLazuliDye oreDictName="dyeBlue"/>
            <blueDye oreDictName="dyeBlue"/>
            <purpleDye oreDictName="dyePurple"/>
            <cyanDye oreDictName="dyeCyan"/>
            <lightGrayDye oreDictName="dyeLightGray"/>
            <grayDye oreDictName="dyeGray"/>
            <pinkDye oreDictName="dyePink"/>
            <limeDye oreDictName="dyeLime"/>
            <dandelionYellow id="dye" damage="11"/>
            <yellowDye oreDictName="dyeYellow"/>
            <lightBlueDye oreDictName="dyeLightBlue"/>
            <magentaDye oreDictName="dyeMagenta"/>
            <orangeDye oreDictName="dyeOrange"/>
        </dye>
    </resources>
    <item>
        <utility>
            <torch id="torch"/>
            <flintAndSteel id="flint_and_steel"/>
            <craftingTable id="crafting_table"/>
            <furnace id="furnace"/>
            <furnaceBurning id="lit_furnace"/>
            <chests>
                <chest id="chest"/>
                <enderChest id="ender_chest"/>
            </chests>
            <beacon id="beacon"/>
            <anvil id="anvil"/>
            <jukebox id="jukebox"/>
            <bed id="bed"/>
            <readableBooks>
                <bookAndQuill id="writable_book"/>
                <writtenBook id="written_book"/>
            </readableBooks>
            <enchantedBook id="enchanted_book"/>
            <tnt id="tnt"/>
            <tripwireHook id="tripwire_hook"/>
            <bowl id="bowl"/>
            <cauldron id="cauldron"/>
            <brewingStand id="brewing_stand"/>
            <enchantmentTable id="enchanting_table"/>
            <eyeOfEnder id="ender_eye"/>
            <enderEye id="ender_eye"/>
            <spawnerEgg id="spawn_egg"/>
            <tripwire id="tripwire"/>
            <nameTag id="name_tag"/>
            <animalLead id="lead"/>
            <horseArmor>
                <ironHorseArmor id="iron_horse_armor"/>
                <goldHorseArmor id="golden_horse_armor"/>
                <diamondHorseArmor id="diamond_horse_armor"/>
            </horseArmor>
        </utility>
        <bucket>
            <emptyBucket id="bucket"/>
            <waterBucket id="water_bucket"/>
            <lavaBucket id="lava_bucket"/>
        </bucket>
        <instrument>
            <clock id="clock"/>
            <compass id="compass"/>
            <emptyMap id="map"/>
            <map id="filled_map"/>
        </instrument>
        <vehicle>
            <boat id="boat"/>
            <minecarts>
                <minecart id="minecart"/>
                <poweredMinecart id="furnace_minecart"/>
                <storageMinecart id="chest_minecart"/>
                <tntMinecart id="tnt_minecart"/>
                <hopperMinecart id="hopper_minecart"/>
                <commandBlockMinecart id="command_block_minecart"/>
            </minecarts>
            <saddle id="saddle"/>
        </vehicle>
        <decoration>
            <bed id="bed"/>
            <door>
                <doorIron id="iron_door"/>
                <doorWood id="wooden_door"/>
            </door>
            <painting id="painting"/>
            <sign id="sign"/>
            <bookShelf id="bookshelf"/>
            <flowerPot id="flower_pot"/>
            <itemFrame id="item_frame"/>
            <frame id="item_frame"/>
            <head id="skull"/>
            <musicDisc>
                <musicDisc13 id="record_13"/>
                <musicDiscCat id="record_cat"/>
                <musicDiscBlocks id="record_blocks"/>
                <musicDiscChirp id="record_chirp"/>
                <musicDiscFar id="record_far"/>
                <musicDiscMall id="record_mall"/>
                <musicDiscMellohi id="record_mellohi"/>
                <musicDiscStal id="record_stal"/>
                <musicDiscStrad id="record_strad"/>
                <musicDiscWard id="record_ward"/>
                <musicDisc11 id="record_11"/>
                <musicDiskWait id="record_wait"/>
            </musicDisc>
            <flowerPotBlock id="flower_pot"/>
            <headBlock id="skull"/>
        </decoration>
        <food>
            <edibleFood>
                <mushroomStew id="mushroom_stew"/>
                <cookedFood>
                    <cookedPorkchop id="cooked_porkchop"/>
                    <cookedFish id="cooked_fished" damage="0"/>
                    <cookedSalmon id="cooked_fished" damage="1"/>
                    <steak id="cooked_beef"/>
                    <cookedChicken id="cooked_chicken"/>
                    <bakedPotato id="baked_potato"/>
                </cookedFood>
                <bread id="bread"/>
                <pumpkinPie id="pumpkin_pie"/>
                <melonSlice id="melon"/>
                <carrot>
                    <carrot id="carrot"/>
                    <goldenCarrot id="golden_carrot"/>
                </carrot>
                <apple>
                    <appleRed id="apple"/>
                    <appleGolden id="golden_apple"/>
                </apple>
                <cookie id="cookie"/>
                <rawFood>
                    <rawPorkchop id="porkchop"/>
                    <rawFish id="fish" damage="0"/>
                    <rawSalmon id="fish" damage="1"/>
                    <clownfish id="fish" damage="2"/>
                    <rawBeef id="beef"/>
                    <rawChicken id="chicken"/>
                    <potato id="potato"/>
                </rawFood>
            </edibleFood>
            <poisonedFood>
                <rottenFlesh id="rotten_flesh"/>
                <spiderEye id="spider_eye"/>
                <poisonedPotato id="poisonous_potato"/>
                <poisonousPotato id="poisonous_potato"/>
                <pufferfish id="fish" damage="3"/>
            </poisonedFood>
            <unedibleFood>
                <cake id="cake"/>
                <sugar id="sugar"/>
                <cakeBlock id="cake"/>
            </unedibleFood>
        </food>
        <plant>
            <flower>
                <flowerYellow id="yellow_flower"/>
                <dandelion id="yellow_flower"/>
                <flowerRed>
                    <poppy id="red_flower" damage="0"/>
                    <tulipRed id="red_flower" damage="4"/>
                </flowerRed>
                <flowerLightBlue id="red_flower" damage="1"/>
                <blueOrchid id="red_flower" damage="1"/>
                <flowerMagenta id="red_flower" damage="2"/>
                <allium id="red_flower" damage="2"/>
                <flowerLightGray>
                    <azureBluet id="red_flower" damage="3"/>
                    <tulipWhite id="red_flower" damage="6"/>
                </flowerLightGray>
                <flowerOrange id="red_flower" damage="5"/>
                <flowerPink id="red_flower" damage="7"/>
                <tulip>
                    <tulipRed id="red_flower" damage="4"/>
                    <tulipWhite id="red_flower" damage="6"/>
                    <tulipOrange id="red_flower" damage="5"/>
                    <tulipPink id="red_flower" damage="7"/>
                </tulip>
                <doublePlant id="double_plant">
                    <sunflower id="double_plant" damage="0"/>
                    <lilac id="double_plant" damage="1"/>
                    <!-- 2 is double-height tallgrass, 3 is large fern -->
                    <roseBush id="double_plant" damage="4"/>
                    <peony id="double_plant" damage="5"/>
                </doublePlant>
            </flower>
            <mushroom>
                <mushroomBrown id="brown_mushroom"/>
                <mushroomRed id="red_mushroom"/>
            </mushroom>
            <sapling>
                <oakSapling id="sapling" damage="0"/>
                <spruceSapling id="sapling" damage="1"/>
                <birchSapling id="sapling" damage="2"/>
                <jungleSapling id="sapling" damage="3"/>
                <acaciaSapling id="sapling" damage="4"/>
                <darkOakSapling id="sapling" damage="5"/>
                <saplingDict oreDictName="treeSapling"/>
            </sapling>
            <leaves id="leaves">
                <oakLeaves id="leaves" damage="0"/>
                <spruceLeaves id="leaves" damage="1"/>
                <birchLeaves id="leaves" damage="2"/>
                <jungleLeaves id="leaves" damage="3"/>
                <acaciaLeaves id="leaves2" damage="0"/>
                <darkOakLeaves id="leaves2" damage="1"/>
                <leavesDict oreDictName="treeLeaves"/>
            </leaves>
            <seed>
                <wheatSeed id="wheat_seeds"/>
                <pumpkinSeed id="pumpkin_seeds"/>
                <melonSeed id="melon_seeds"/>
            </seed>
            <netherWart id="nether_wart"/>
            <netherWart id="nether_wart"/>
            <sugarCane id="reeds"/>
            <sugarCane id="reeds"/>
            <vines id="vine"/>
            <cactus id="cactus"/>
            <reed id="reeds"/>
            <melon id="melon_block"/>
            <pumpkin id="pumpkin"/>
            <cocoaPod id="cocoa"/>
            <cocoaPlant id="cocoa"/>
            <tallGrass id="tallgrass"/>
            <deadShrub id="deadbush"/>
            <deadBush id="deadbush"/>
            <seedBlock id="wheat"/>
            <pumpkinStem id="pumpkin_stem"/>
            <melonStem id="melon_stem"/>
            <carrotBlock id="carrots"/>
            <potatoBlock id="potatoes"/>
            <potatoesBlock id="potatoes"/>
            <lilyPad id="waterlily"/>
        </plant>
        <fireworks id="firework_charge"/>
        <potion>
            <drinkablePotion>
                <neutralPotion>
                    <waterBottle id="potion" damage="0"/>
                    <awkwardPotion id="potion" damage="16"/>
                    <thickPotion id="potion" damage="32"/>
                    <mundanePotionExtended id="potion" damage="64"/>
                    <mundanePotion id="potion" damage="8192"/>
                </neutralPotion>
                <positivePotion>
                    <regenerationPotion>
                        <potionRegeneration id="potion" damage="8193"/>
                        <potionRegenerationEx id="potion" damage="8257"/>
                        <potionRegenerationII id="potion" damage="8225"/>
                    </regenerationPotion>
                    <swiftnessPotion>
                        <potionSwiftness id="potion" damage="8194"/>
                        <potionSwiftnessEx id="potion" damage="8258"/>
                        <potionSwiftnessII id="potion" damage="8226"/>
                    </swiftnessPotion>
                    <fireResistancePotion>
                        <potionFireResistance id="potion" damage="8195"/>
                        <potionFireResistanceEx id="potion" damage="8259"/>
                        <potionFireResistanceII id="potion" damage="8227"/>
                    </fireResistancePotion>
                    <healingPotion>
                        <potionHealing id="potion" damage="8197"/>
                        <potionHealingEx id="potion" damage="8261"/>
                        <potionHealingII id="potion" damage="8229"/>
                    </healingPotion>
                    <strengthPotion>
                        <potionStrength id="potion" damage="8201"/>
                        <potionStrengthEx id="potion" damage="8265"/>
                        <potionStrengthII id="potion" damage="8233"/>
                    </strengthPotion>
                    <nightVisionPotion>
                        <potionNightVision id="potion" damage="8230"/>
                        <potionNightVisionEx id="potion" damage="8262"/>
                    </nightVisionPotion>
                    <invisibilityPotion>
                        <potionInvisibility id="potion" damage="8238"/>
                        <potionInvisibilityEx id="potion" damage="8270"/>
                    </invisibilityPotion>
                </positivePotion>
                <negativePotion>
                    <poisonPotion>
                        <potionPoison id="potion" damage="8196"/>
                        <potionPoisonEx id="potion" damage="8260"/>
                        <potionPoisonII id="potion" damage="8228"/>
                    </poisonPotion>
                    <weaknessPotion>
                        <potionWeakness id="potion" damage="8200"/>
                        <potionWeaknessEx id="potion" damage="8264"/>
                        <potionWeaknessII id="potion" damage="8232"/>
                    </weaknessPotion>
                    <slownessPotion>
                        <potionSlowness id="potion" damage="8202"/>
                        <potionSlownessEx id="potion" damage="8266"/>
                        <potionSlownessII id="potion" damage="8234"/>
                    </slownessPotion>
                    <harmingPotion>
                        <potionHarming id="potion" damage="8204"/>
                        <potionHarmingEx id="potion" damage="8268"/>
                        <potionHarmingII id="potion" damage="8236"/>
                    </harmingPotion>
                </negativePotion>
            </drinkablePotion>
            <splashPotion>
                <splashMundanePotion id="potion" damage="16384"/>
                <positiveSplashPotion>
                    <regenerationSplashPotion>
                        <splashPotionRegeneration id="potion" damage="16385"/>
                        <splashPotionRegenerationEx id="potion" damage="16449"/>
                        <splashPotionRegenerationII id="potion" damage="16417"/>
                    </regenerationSplashPotion>
                    <swiftnessSplashPotion>
                        <splashPotionSwiftness id="potion" damage="16386"/>
                        <splashPotionSwiftnessEx id="potion" damage="16450"/>
                        <splashPotionSwiftnessII id="potion" damage="16418"/>
                    </swiftnessSplashPotion>
                    <fireResistanceSplashPotion>
                        <splashPotionFireResistance id="potion" damage="16387"/>
                        <splashPotionFireResistanceEx id="potion" damage="16451"/>
                        <splashPotionFireResistanceII id="potion" damage="16419"/>
                    </fireResistanceSplashPotion>
                    <healingSplashPotion>
                        <splashPotionHealing id="potion" damage="16389"/>
                        <splashPotionHealingEx id="potion" damage="16453"/>
                        <splashPotionHealingII id="potion" damage="16421"/>
                    </healingSplashPotion>
                    <strengthSplashPotion>
                        <splashPotionStrength id="potion" damage="16393"/>
                        <splashPotionStrengthEx id="potion" damage="16457"/>
                        <splashPotionStrengthII id="potion" damage="16425"/>
                    </strengthSplashPotion>
                    <nightVisionSplashPotion>
                        <splashPotionNightVision id="potion" damage="16422"/>
                        <splashPotionNightVisionEx id="potion" damage="16454"/>
                    </nightVisionSplashPotion>
                    <invisibilitySplashPotion>
                        <splashPotionInvisibility id="potion" damage="16430"/>
                        <splashPotionInvisibilityEx id="potion" damage="16462"/>
                    </invisibilitySplashPotion>
                </positiveSplashPotion>
                <negativeSplashPotion>
                    <poisonSplashPotion>
                        <splashPotionPoison id="potion" damage="16388"/>
                        <splashPotionPoisonEx id="potion" damage="16452"/>
                        <splashPotionPoisonII id="potion" damage="16420"/>
                    </poisonSplashPotion>
                    <weaknessSplashPotion>
                        <splashPotionWeakness id="potion" damage="16392"/>
                        <splashPotionWeaknessEx id="potion" damage="16456"/>
                        <splashPotionWeaknessII id="potion" damage="16424"/>
                    </weaknessSplashPotion>
                    <slownessSplashPotion>
                        <splashPotionSlowness id="potion" damage="16394"/>
                        <splashPotionSlownessEx id="potion" damage="16458"/>
                        <splashPotionSlownessII id="potion" damage="16426"/>
                    </slownessSplashPotion>
                    <harmingSplashPotion>
                        <splashPotionHarming id="potion" damage="16396"/>
                        <splashPotionHarmingEx id="potion" damage="16460"/>
                        <splashPotionHarmingII id="potion" damage="16428"/>
                    </harmingSplashPotion>
                </negativeSplashPotion>
            </splashPotion>
            <bottleOEnchanting id="experience_bottle"/>
            <bottleEnchanting id="experience_bottle"/>
        </potion>
        <glassBottle id="glass_bottle"/>
        <potionIngredient>
            <netherWart id="nether_wart"/>
            <redstone id="redstone"/>
            <glowstoneDust id="glowstone_dust"/>
            <sugar id="sugar"/>
            <ghastTear id="ghast_tear"/>
            <spiderEye id="spider_eye"/>
            <fermentedSpiderEye id="fermented_spider_eye"/>
            <blazePowder id="blaze_powder"/>
            <magmaCream id="magma_cream"/>
            <glisteringMelon id="speckled_melon"/>
        </potionIngredient>
    </item>
</stuff>
