{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 37}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "In order to craft even basic machines, you'll need new tools. Most of them can be replaced by machines later. For now, you should become familiar with each of their recipes, as you will need a lot of these tools, starting today.\n\nYour new tools can also be used to make Railcraft water tanks, a very useful multiblock considering the finite water feature.\n\nThere are also electric versions of the tools, you can upgrade later if you want. Also, there are many versions of the tools, they generally follow the same rules as tinkers parts in terms of how good they are, similarly to turbines. You can look at the spreadsheet for more info on GT tool stats.\n\nFor this quest, you can use whatever materials you want. The gem version of the saw is crafted differently to metal versions. Higher tier tools need steel or tungstensteel rods instead of sticks.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 16, "OreDict:8": "", "id:8": "gregtech:gt.metatool.01"}, "ignoreNBT:1": 1, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 1, "name:8": "§2§lImportant Tools", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 36, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}, "1:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "harvestcraft:zucchinibakeItem"}}}}, "tasks:9": {"0:10": {"allowAnvil:1": 0, "allowCraft:1": 1, "allowCraftedFromStatistics:1": 0, "allowSmelt:1": 1, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 12, "OreDict:8": "", "id:8": "gregtech:gt.metatool.01", "tag:10": {"GT.ToolStats:10": {"MaxDamage:4": 25600, "PrimaryMaterial:8": "Iron", "SecondaryMaterial:8": "<PERSON>"}, "ench:9": {"0:10": {"id:2": 16, "lvl:2": 1}}}}}, "taskID:8": "bq_standard:crafting"}, "1:10": {"allowAnvil:1": 0, "allowCraft:1": 1, "allowCraftedFromStatistics:1": 0, "allowSmelt:1": 1, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 16, "OreDict:8": "", "id:8": "gregtech:gt.metatool.01", "tag:10": {"GT.ToolStats:10": {"MaxDamage:4": 25600, "PrimaryMaterial:8": "Iron", "SecondaryMaterial:8": "Iron"}}}}, "taskID:8": "bq_standard:crafting"}, "2:10": {"allowAnvil:1": 0, "allowCraft:1": 1, "allowCraftedFromStatistics:1": 0, "allowSmelt:1": 1, "ignoreNBT:1": 1, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 18, "OreDict:8": "", "id:8": "gregtech:gt.metatool.01", "tag:10": {"GT.ToolStats:10": {"MaxDamage:4": 25600, "PrimaryMaterial:8": "Iron", "SecondaryMaterial:8": "<PERSON>"}}}}, "taskID:8": "bq_standard:crafting"}, "3:10": {"allowAnvil:1": 0, "allowCraft:1": 1, "allowCraftedFromStatistics:1": 0, "allowSmelt:1": 1, "ignoreNBT:1": 1, "index:3": 3, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 22, "OreDict:8": "", "id:8": "gregtech:gt.metatool.01", "tag:10": {"GT.ToolStats:10": {"MaxDamage:4": 25600, "PrimaryMaterial:8": "Iron", "SecondaryMaterial:8": "<PERSON>"}}}}, "taskID:8": "bq_standard:crafting"}, "4:10": {"allowAnvil:1": 0, "allowCraft:1": 1, "allowCraftedFromStatistics:1": 0, "allowSmelt:1": 1, "ignoreNBT:1": 1, "index:3": 4, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 10, "OreDict:8": "", "id:8": "gregtech:gt.metatool.01", "tag:10": {"GT.ToolStats:10": {"MaxDamage:4": 25600, "PrimaryMaterial:8": "Iron", "SecondaryMaterial:8": "<PERSON>"}}}}, "taskID:8": "bq_standard:crafting"}}}