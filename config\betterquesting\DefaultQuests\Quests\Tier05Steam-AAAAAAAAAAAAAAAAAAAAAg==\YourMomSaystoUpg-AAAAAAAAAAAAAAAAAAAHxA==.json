{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1987}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "There are many tiers of upgrades for storage drawers. Make some iron and gold tier upgrades and I'll give you some of the special purpose upgrades to experiment with in your base. Check NEI for more options such as Obsidian, Emerald, and Diamond upgrades.\n\n[warn]To remove upgrades, shift-right-click with an empty hand to access the drawer interface.[/warn]", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "StorageDrawers:upgrade"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§3§lYour Mom Says to Upgrade Your Drawers", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 1988, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "StorageDrawers:upgradeStatus"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "StorageDrawers:upgradeVoid"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "StorageDrawers:upgrade"}, "1:10": {"Count:3": 1, "Damage:2": 3, "OreDict:8": "", "id:8": "StorageDrawers:upgrade"}}, "taskID:8": "bq_standard:retrieval"}}}