{"preRequisites:9": {"0:10": {"questIDHigh:4": -7295214463238520795, "questIDLow:4": -8557295371763424843}, "1:10": {"questIDHigh:4": -139068609484536132, "questIDLow:4": -5753871526049634361}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "You got to take a well deserved break from the waterline in UHV, but now that you are going to be making optical circuits, your Grade 6 Purified Water will no longer be enough. Using the Degasser, you can remove all the leftover decontaminants from previous processes and make even more pure water, pure enough for Optical Circuits.\n\nThe tooltip and list of requirements looks intimidating. Try breaking it down into small, digestible units of logic and you will quickly arrive at a solution.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 9412, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "The Quest for Holy Water", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 6411465645269994516, "questIDLow:4": -5575624123602641916, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 16, "Damage:2": 9649, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 9412, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 11, "OreDict:8": "", "id:8": "gregtech:gt.blockcasings9"}, "1:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "gregtech:gt.blockglass1"}, "2:10": {"Count:3": 1, "Damage:2": 395, "OreDict:8": "", "id:8": "gregtech:gt.blockframes"}, "3:10": {"Count:3": 1, "Damage:2": 9413, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:optional_retrieval"}}}