{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1535}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "This is an all-in-one solution to generate oxygen. Just provide power (continuous) and Seeds (once).", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "GalacticraftAmunRa:tile.machines4"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "A Better Way To Generate Oxygen", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": -4603000547969448882, "questIDLow:4": -8825008741784511339, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 23, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "GalacticraftAmunRa:tile.machines4"}}, "taskID:8": "bq_standard:retrieval"}}}