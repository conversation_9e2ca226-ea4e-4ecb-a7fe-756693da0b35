{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2714}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "So if you looked in NEI and tried to figure out how to make this, you were probably confused by the fact that you use the fluid it produces to make it. This is where NEI is lying to you. Like the previous recipe, NEI doesn't show it correctly (they should be around the same page in NEI, they both also appear to produce Sterilized Petri Dishes, (even if that's not true) and both use unique Modules).\n\nIn order to make this, you'll need to make the Rotten Flesh Bacteria culture, get the DNA Sample, and then get the Data Orb. It changes its name to beta-Lactamase, that's normal.\n\nThen, do the same thing for Common Yeast (get the right Yeast!).\n\nThen you need to use the recipe to combine the information from the Data Orbs and get the Common Yeast Plasmid from that.\n\nLastly, use the Rotten Flesh Bacteria culture, the Common Yeast Plasmid, Penicillin (make it by centrifuging Common Yeast Fluid), and Distilled Water. You want the Transformation Module for this. It's a 100% chance.\n\nYou'll want to use the normal recipe to make more cultures, since it also becomes beta-Lactamase once you turn it into a DNA Sample.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Breedable:1": 1, "Color:11": [127, 69, 26], "DNA:10": {"Chance:3": 10000, "Name:8": "beta-Lactamase", "Rarity:1": 1, "Tier:3": 0}, "Fluid:8": "saccharomycesescherichiafluid", "Name:8": "Saccharomyces escherichia", "Plasmid:10": {"Chance:3": 7500, "Name:8": "Saccharomyces cerevisiae", "Rarity:1": 0, "Tier:3": 0}, "Rarety:1": 2}}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "General Purpose Fermenting Bacteria", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2715, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Breedable:1": 1, "Color:11": [149, 132, 75], "DNA:10": {"Chance:3": 10000, "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Rarity:1": 1, "Tier:3": 0}, "Fluid:8": "escherichiakolifluid", "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Plasmid:10": {"Chance:3": 10000, "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Rarity:1": 1, "Tier:3": 0}, "Rarety:1": 1}}, "1:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Chance:3": 10000, "Name:8": "beta-Lactamase", "Rarity:1": 1, "Tier:3": 0}}, "2:10": {"Count:3": 1, "Damage:2": 32707, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01", "tag:10": {"mDataName:8": "beta-Lactamase", "mDataTitle:8": "DNA Sample"}}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Breedable:1": 1, "Color:11": [255, 248, 200], "DNA:10": {"Chance:3": 7500, "Name:8": "Saccharomyces cerevisiae", "Rarity:1": 0, "Tier:3": 0}, "Fluid:8": "saccharomycescerevisiaefluid", "Name:8": "Saccharomyces cerevisiae", "Plasmid:10": {"Chance:3": 7500, "Name:8": "Saccharomyces cerevisiae", "Rarity:1": 0, "Tier:3": 0}, "Rarety:1": 0}}, "1:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Chance:3": 7500, "Name:8": "Saccharomyces cerevisiae", "Rarity:1": 0, "Tier:3": 0}}, "2:10": {"Count:3": 1, "Damage:2": 32707, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01", "tag:10": {"mDataName:8": "Saccharomyces cerevisiae", "mDataTitle:8": "DNA Sample"}}}, "taskID:8": "bq_standard:retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 10, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.98"}, "1:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "bartworks:BioLabParts"}, "2:10": {"Count:3": 1, "Damage:2": 32707, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01", "tag:10": {"mDataName:8": "Saccharomyces cerevisiae", "mDataTitle:8": "DNA Sample"}}, "3:10": {"Count:3": 1, "Damage:2": 32707, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01", "tag:10": {"mDataName:8": "beta-Lactamase", "mDataTitle:8": "DNA Sample"}}, "4:10": {"Count:3": 1, "Damage:2": 16, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.98"}}, "taskID:8": "bq_standard:retrieval"}, "3:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 3, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "bartworks:BioLabModules"}}, "taskID:8": "bq_standard:retrieval"}, "4:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 4, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Chance:3": 7500, "Name:8": "Saccharomyces cerevisiae", "Rarity:1": 0, "Tier:3": 0}}}, "taskID:8": "bq_standard:retrieval"}, "5:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 5, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Breedable:1": 0, "Color:11": [110, 40, 25], "DNA:10": {"Chance:3": 10000, "Name:8": "beta-Lactamase", "Rarity:1": 1, "Tier:3": 0}, "Name:8": "Escherichia cadaver", "Plasmid:10": {"Chance:3": 10000, "Name:8": "beta-Lactamase", "Rarity:1": 1, "Tier:3": 0}, "Rarety:1": 1}}, "1:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Chance:3": 7500, "Name:8": "Saccharomyces cerevisiae", "Rarity:1": 0, "Tier:3": 0}}, "2:10": {"Count:3": 1, "Damage:2": 12, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.98"}, "3:10": {"Count:3": 1, "Damage:2": 12, "OreDict:8": "", "id:8": "IC2:itemCellEmpty"}}, "taskID:8": "bq_standard:retrieval"}, "6:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 6, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 3, "OreDict:8": "", "id:8": "bartworks:BioLabModules"}}, "taskID:8": "bq_standard:retrieval"}, "7:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 7, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Breedable:1": 1, "Color:11": [127, 69, 26], "DNA:10": {"Chance:3": 10000, "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Rarity:1": 1, "Tier:3": 0}, "Fluid:8": "saccharomycesescherichiafluid", "Name:8": "Saccharomyces escherichia", "Plasmid:10": {"Chance:3": 7500, "Name:8": "Saccharomyces cerevisiae", "Rarity:1": 0, "Tier:3": 0}, "Rarety:1": 2}}}, "taskID:8": "bq_standard:retrieval"}}}