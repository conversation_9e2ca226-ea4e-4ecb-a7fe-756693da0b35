{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2059}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 1951}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Knightmetal is the top tier Twilight Forest armor. It doesn't come with any free enchants but it has high durability and high armor rating.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.knightlyPlate"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "\"Oh, <PERSON>\" Armor III", "partySingleReward:1": 0, "questLogic:8": "OR", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2058, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.knightlySword"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.knightlyPick"}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.knightlyAxe"}, "3:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinDarkWizard"}, "1:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSmith"}, "2:10": {"Count:3": 25, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSurvivor"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 24, "Damage:2": 17362, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.knightlyHelm"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.knightlyPlate"}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.knightlyLegs"}, "3:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TwilightForest:item.knightlyBoots"}}, "taskID:8": "bq_standard:retrieval"}}}