{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1496}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "At the top of your map screen (<PERSON> by default) there are multiple tabs, some of them will be very useful and will save you a ton of searching!\n\nGT Oreveins - this tab will keep track of the veins for you, to save a vein to map right-click a naturally generated ore (some tools can't right-click)\nGT Underground fluids - this will help you later (MV)\nTC Nodes - this tab will keep track of thaumcraft nodes for you (you need to scan them first)\n\n[note]Small Ores are not part of veins and thus do not uncover a vein on the map. However veins generate extra small ores related to the vein in their chunks, so they can still be useful in your search (though not all small ores are related to a nearby vein so don't be fooled).[/note]\n\n[note]Another great interaction is that you can use the NEI search bar in highlight mode (double-click it) and then go to the map screen to get the matching uncovered ore veins highlighted![/note]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:filled_map"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§6§lThe power of prospecting!", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 7836976352036995675, "questIDLow:4": -7957069611279940583, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "harvestcraft:herbbutterparsnipsItem"}}}}, "tasks:9": {"0:10": {"index:3": 0, "taskID:8": "bq_standard:checkbox"}}}