{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2304}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "The Platinum Group processing line (AKA Platline) is a multi-step complicated process. Because of its complexity, and how much you need, you should have a processing line running all the time. You should be able to comprehend it better after doing these quests. Or maybe you set it up already, and you're siphoning off some of the results? Either way.\n\nAs a first step in getting Rhodium, Ruthenium, Iridium, and Osmium, we will split apart the Rhodium from the other metals in an EBF with the Potassium Disulfate you made previously.\n\nThe amounts for this step are pretty annoying, blame <PERSON> for that.\n\n[warn]Your output hatch needs to be at the bottom to collect the Rhodium Sulfate.[/warn]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 61, "OreDict:8": "cellRhodiumSulfate", "id:8": "bartworks:gt.bwMetaGeneratedcell"}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§b§lFirst Separation - Rhodium", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "CHAIN"}}, "questIDHigh:4": 0, "questIDLow:4": 2305, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinChemistIII"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianIII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 31, "Damage:2": 49, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGenerateddust"}, "1:10": {"Count:3": 78, "Damage:2": 59, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcellMolten"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 31, "Damage:2": 60, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGenerateddust"}, "1:10": {"Count:3": 11, "Damage:2": 61, "OreDict:8": "", "id:8": "bartworks:gt.bwMetaGeneratedcell"}}, "taskID:8": "bq_standard:retrieval"}}}