{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 165}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 141}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "By utilizing large amounts of energy, you're able to reactivate those broken spawners you may have found in the past. If you haven't, you should get one of them now.\n\nIf the broken spawner isn't of the type of mob you want to spawn, you'll need to do some extra work. Capture one in a soul vial, then use that and the broken spawner in the soul binder to change the type. This takes forever, and also levels.\n\nCombine a BS with your Powered Spawner in an anvil to set the type for it to spawn. Then you just gotta power it up!\n\nIf you set it to capture mode, you can make souls of a specific mob without any danger.\n\n[note]Please try to use as few as possible on servers, mobs are laggy. Use crops if possible.[/note]", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:blockPoweredSpawner"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§9§lThe Creation of Life", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 169, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 6, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}, "1:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemBasicCapacitor"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianI"}, "1:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinDarkWizardI"}, "2:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSurvivorI"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:itemBrokenSpawner"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "EnderIO:blockPoweredSpawner"}}, "taskID:8": "bq_standard:retrieval"}}}