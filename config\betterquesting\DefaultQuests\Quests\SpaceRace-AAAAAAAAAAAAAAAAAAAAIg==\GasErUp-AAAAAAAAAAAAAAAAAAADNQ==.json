{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 819}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "To pump fuel into your rocket you need a fuel loader. Place it next to any side of the launch pad. The green side is for energy, while the yellow is for fuel. Pipe it in if you don't want to lose your cells.\n\nYou can also power it with batteries, if you're worried about it draining your power, or don't want to set up the infrastructure.\n\n[note]Rockets keep their fuel in them on landing now, so you don't need to take this with you anymore unless you want to go to multiple places in one trip. Or you didn't fill it all the way or something.[/note]", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "GalacticraftCore:tile.fuelLoader"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Gas 'Er Up", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 821, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 1, "Damage:2": 30709, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "1:10": {"Count:3": 2, "Damage:2": 9, "OreDict:8": "", "id:8": "GalacticraftCore:item.basicItem"}, "2:10": {"Count:3": 1, "Damage:2": 6, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 2, "Damage:2": 9, "OreDict:8": "", "id:8": "GalacticraftCore:item.basicItem"}, "1:10": {"Count:3": 2, "Damage:2": 8, "OreDict:8": "", "id:8": "GalacticraftCore:item.basicItem"}, "2:10": {"Count:3": 1, "Damage:2": 32602, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "3:10": {"Count:3": 1, "Damage:2": 32612, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "4:10": {"Count:3": 1, "Damage:2": 5132, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "5:10": {"Count:3": 1, "Damage:2": 13, "OreDict:8": "", "id:8": "GalacticraftCore:item.basicItem"}, "6:10": {"Count:3": 1, "Damage:2": 32405, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "GalacticraftCore:tile.fuelLoader"}}, "taskID:8": "bq_standard:retrieval"}}}