{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 35}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 1866}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 30}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "With zinc and copper, you can create a material with a higher melting temperature - Brass - which can be used to cast better tools.\n\nFor that, you need a smeltery. See the Multiblock Goals tab for details. You can also grab some extra blocks from some villages, but they don't count towards the quest.\n\nAlternatively, there are also one-time-use clay casts available.\n\nYou can then use your fancy bronze to make better tools with a higher mining level.\n\nRemember, you need to use the pickaxe a bit to gather mining XP if you want the full mining level on your bronze pickaxe. Certain tool upgrades can give that directly if you have an upgrade slot available.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:pickaxe", "tag:10": {"InfiTool:10": {"Accessory:4": 14, "Attack:4": 4, "BaseAttack:4": 4, "BaseDurability:4": 356, "BonusDurability:4": 0, "Broken:4": 0, "Built:4": 1, "Damage:4": 0, "Handle:4": 14, "HarvestLevel:4": 3, "HarvestLevelModified:4": 0, "Head:4": 14, "HeadEXP:4": 0, "MiningSpeed:4": 650, "ModDurability:6": 0.0, "Modifiers:4": 0, "RenderAccessory:4": 14, "RenderHandle:4": 14, "RenderHead:4": 14, "Shoddy:6": 0.0, "ToolEXP:4": 0, "ToolLevel:4": 1, "TotalDurability:4": 356, "Unbreaking:4": 1}, "display:10": {"Name:8": "Bronze Pickaxe"}}}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§2§lMaking Better Tools", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 38, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 2, "Damage:2": 32243, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.02"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSmith"}, "1:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "TConstruct:metalPattern"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 3, "OreDict:8": "", "id:8": "TConstruct:metalPattern"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 14, "OreDict:8": "", "id:8": "TConstruct:pickaxeHead"}}, "taskID:8": "bq_standard:retrieval"}, "3:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 3, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 14, "OreDict:8": "", "id:8": "TConstruct:shovelHead"}}, "taskID:8": "bq_standard:retrieval"}}}