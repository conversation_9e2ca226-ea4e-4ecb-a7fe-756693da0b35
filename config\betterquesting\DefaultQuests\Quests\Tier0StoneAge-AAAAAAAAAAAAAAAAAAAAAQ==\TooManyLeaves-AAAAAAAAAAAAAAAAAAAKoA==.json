{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 36}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Want to clear out shrubbery but don't want to pull out leaves by hand? Need to find that last log hidden in the middle of a bunch of leaves?\n\nTry an Iron Scythe! When used, it clears out a 5x5x5 area of leaves, crops, flowers, or grass. Excellent for tearing through Sacred Oak leaves or dense shrubbery in certain biomes. Later when you have a Cutting Machine you can upgrade to a Diamond Scythe with a 7x7x7 area of effect, or use Amethyst for 9x9x9 to FLEX.\n\nThe lower tiers break leaves instantly but don't seem to have the 3x3x3 range for some reason.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "BiomesOPlenty:scytheIron"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§2§lToo Many Leaves", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2720, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 4, "Damage:2": 32, "OreDict:8": "", "id:8": "gregtech:gt.blockores"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "BiomesOPlenty:scytheIron"}, "2:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinFarmer"}, "1:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSmith"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "BiomesOPlenty:scytheIron"}}, "taskID:8": "bq_standard:retrieval"}}}