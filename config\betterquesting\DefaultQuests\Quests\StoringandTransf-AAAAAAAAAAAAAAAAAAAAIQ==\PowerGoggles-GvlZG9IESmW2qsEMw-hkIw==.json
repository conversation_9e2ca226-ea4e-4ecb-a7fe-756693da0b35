{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2732}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Wanna see how your LSC is doing but don't want to run to it or your information panel every time? Get yourself a pair of §6§oPower Goggles§r to see your energy storage straight from the HUD!\n\nBy §adefault§f§r the goggles are §cunlinked§r and will monitor the amount of energy in your wireless network.\n\nTo link the goggles to an LSC, §cRight+Click§r the controller with the goggles in your hand. §cShift+Right Click§f§r air to unlink.\n\nIf you wish to move or modify the display you can do so by opening the power goggles config menu!\n\n[warn]Make sure to check your keybinds! There's also a keybind for toggling the power chart![/warn]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "gregtech:gt.Power_Goggles"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Power Goggles", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 1943682690209565285, "questIDLow:4": -5284198952195300317, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "gregtech:gt.Power_Goggles"}}, "taskID:8": "bq_standard:retrieval"}}}