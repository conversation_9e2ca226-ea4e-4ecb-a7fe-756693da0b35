{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 44}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Your smeltery does a great job mixing metals into alloys, but it's not very efficient, and limited in its complexity. You need something new, so why not put your steam to some use? The Alloy Smelter can make bronze for you directly, using ingots, dusts, or even nuggets. You can make a nugget mold as well to turn ingots into nuggets if you have steel.\n\nIt might also come in handy when processing raw rubber. You should keep an eye on recipes to see when machines offer better options to make items...\n\n[note]NOTE: Like all steam machines, the output port is a steam outlet when recipes complete. It must not be blocked. Also, steam is hot, remember? Change the output side by whacking the machine with a wrench. The 3x3 grid will show you which side the output will move to when you whack it.[/note]\n\nYou can empty the machine by putting a hopper beneath it.\n\n[warn]WARNING: The Alloy Smelter recipes take a lot of steam.[/warn] Do them one by one until you understand how fast you use steam, and how much you will need buffered. Large pipes are a decent \"poor man's\" buffer, and the boiler can hold quite a bit of steam, as well as the machine itself. Once you get a little further along, make a larger steam buffer with an iron or steel railcraft tank.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 118, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§3§lMore Advanced Alloys", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 46, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 4, "Damage:2": 11300, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 15, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}}}}, "tasks:9": {"0:10": {"allowAnvil:1": 0, "allowCraft:1": 1, "allowCraftedFromStatistics:1": 0, "allowSmelt:1": 1, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:crafting"}, "1:10": {"allowAnvil:1": 0, "allowCraft:1": 1, "allowCraftedFromStatistics:1": 0, "allowSmelt:1": 1, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 118, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:crafting"}}}