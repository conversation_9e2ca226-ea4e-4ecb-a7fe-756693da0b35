{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 36}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "You have found a way to make leather more durable and resistant by further processing it. Make some bound leather from four pieces of leather, some strings and woven cotton. Now you only have to hang it on a drying rack and wait about 10 minutes to get some tanned leather. Stockpile some for later.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "Backpack:tanned<PERSON><PERSON><PERSON>"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§2§lTanned Leather", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 486, "rewards:9": {"0:10": {"choices:9": {"0:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "harvestcraft:wovencottonItem"}, "1:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:iron_ingot"}, "2:10": {"Count:3": 1, "Damage:2": 1, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}, "ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:choice"}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:leather"}, "1:10": {"Count:3": 4, "Damage:2": 0, "OreDict:8": "", "id:8": "minecraft:string"}, "2:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "harvestcraft:wovencottonItem"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "TConstruct:<PERSON>or.DryingRack"}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "Backpack:<PERSON><PERSON><PERSON><PERSON>"}}, "taskID:8": "bq_standard:optional_retrieval"}, "3:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 3, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "Backpack:tanned<PERSON><PERSON><PERSON>"}}, "taskID:8": "bq_standard:retrieval"}}}