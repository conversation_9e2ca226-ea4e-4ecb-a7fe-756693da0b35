{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 176}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 1225}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 394}, "3:10": {"questIDHigh:4": 0, "questIDLow:4": 861}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "IV-Level. You will need Tungstensteel for machines. Suggested planets and moons to visit: Mars, Phobos, Deimos.\n\nMake sure to get a set of TPV coils for your EBF.\n\nYou should've learned \"how to GregTech\" by now. If not, go clean up the machine craters in your base...\n\nMany GT++ multiblock machines unlock at this tier. Make sure you have an Alloy Blast Smelter ready to go and check the GT++ tab.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 15, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§b§lTier 5 (IV)", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "tierthemes:iv", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 213, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 8, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinAdventureII"}, "1:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinSpaceII"}, "2:10": {"Count:3": 5, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnicianII"}}}}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 15, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:retrieval"}}}