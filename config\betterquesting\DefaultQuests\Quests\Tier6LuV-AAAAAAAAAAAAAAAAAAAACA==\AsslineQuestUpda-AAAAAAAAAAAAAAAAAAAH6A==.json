{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 1493}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "When the pack updates and changes an assembly line recipe, your old data sticks also need to change their recipe. In old versions, you needed to update the sticks by scanning new items, but now they will update automatically if placed in the assembly line, so you don't need to change things manually. If there is some kind of bug with data stick recipes after updating, do let the developers know, either on the Discord server or on GitHub.", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 32608, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§c§lAssline Quest Updates", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2024, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 1, "Damage:2": 41, "OreDict:8": "", "id:8": "enhancedlootbags:lootbag", "tag:10": {"ench:9": {"0:10": {"id:4": 35, "lvl:4": 3}}}}}}}, "tasks:9": {"0:10": {"index:3": 0, "taskID:8": "bq_standard:checkbox"}}}