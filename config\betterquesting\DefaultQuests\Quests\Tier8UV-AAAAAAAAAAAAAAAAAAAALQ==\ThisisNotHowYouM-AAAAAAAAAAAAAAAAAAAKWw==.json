{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2561}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 2566}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 2635}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "In order to make Wetware circuits, you'll need Sterilized Growth Catalyst Medium and Stemcells.\n\nTo begin, you will first need Bacteria which you can get through the Bacterial Vat or the Brewing Machine. The vat recipe is more efficient, but you need a culture for it so you need to start with the brewing recipe. The Bacterial Vat needs at least Bismuth rods in the Radio Hatch for the proper Sievert and at least EV-tier glass to make Bacteria. There are also 3 different recipes, so you can use a higher tier one later if you like.\n\nYou'll also need Mince Meat and Agar. You'll want a stable supply of meat such as a fish farm, a standard animal farm, or IC2 Meatrose/Goldfish farm.\n\nAgar has a more complicated processing chain, but it's nothing compared to Platinum. You'll need even more meat for it.\n\nUse some of the Bacteria to create an Ova Evolutionis culture (15% chance), and then place that in the Bacterial Vat's controller. The Bacterial Vat also needs at least Uranium rods in the Radio Hatch for the proper Sievert and at least IV-tier glass to make Raw Growth Catalyst Medium. There are higher tier recipes for this as well.\n\n[note]Cultures are NOT consumed in the Bacterial Vat.[/note]", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Breedable:1": 0, "Color:11": [223, 206, 155], "DNA:10": {"Chance:3": 1500, "Name:8": "OvumBac", "Rarity:1": 3, "Tier:3": 2}, "Name:8": "<PERSON><PERSON>", "Plasmid:10": {"Chance:3": 1500, "Name:8": "OvumBac", "Rarity:1": 3, "Tier:3": 2}, "Rarety:1": 3}}, "isMain:1": 1, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "This is Not How You Make Cheese", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2651, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 15, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.98"}}, "taskID:8": "bq_standard:optional_retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 2, "OreDict:8": "", "id:8": "dreamcraft:GTNHBioItems"}, "1:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Breedable:1": 0, "Color:11": [223, 206, 155], "DNA:10": {"Chance:3": 1500, "Name:8": "OvumBac", "Rarity:1": 3, "Tier:3": 2}, "Name:8": "<PERSON><PERSON>", "Plasmid:10": {"Chance:3": 1500, "Name:8": "OvumBac", "Rarity:1": 3, "Tier:3": 2}, "Rarety:1": 3}}}, "taskID:8": "bq_standard:optional_retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 30608, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01"}}, "taskID:8": "bq_standard:retrieval"}}}