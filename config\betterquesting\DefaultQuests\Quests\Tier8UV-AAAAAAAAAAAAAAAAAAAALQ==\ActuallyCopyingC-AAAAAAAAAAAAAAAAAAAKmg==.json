{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 2713}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "To copy a culture, you'll need\n1) A Sterilized Petri Dish\n2) A Plasma Membrane (made from Stemcells, or gotten while making EnzymesSollution)\n3) Stemcells\n4) A Data Orb with the appropriate data on it\n5) Liquid DNA\n6) A UV Bio Lab\n7) A Clonal Cellular Synthesis Module\n\nYou can change out the Data Orb for one with whatever culture you wish to copy. Keep in mind that it might be easier to make a second culture the normal way, and that there's generally an alternative recipe that uses the fluid it produces to make it with a higher chance.\n\nPS: This recipe is the only one in NEI that uses this module, so you can find it if you look, but the number will change, so I can't specify which page.", "globalShare:1": 0, "icon:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "bartworks:BioLabModules"}, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "Actually Copying Cultures", "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 2714, "rewards:9": {}, "tasks:9": {"0:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BioLabParts"}, "1:10": {"Count:3": 1, "Damage:2": 6, "OreDict:8": "", "id:8": "bartworks:BioLabParts"}, "2:10": {"Count:3": 2, "Damage:2": 32073, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.03"}, "3:10": {"Count:3": 1, "Damage:2": 32707, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.01", "tag:10": {"mDataName:8": "<PERSON>scher<PERSON><PERSON> koli", "mDataTitle:8": "DNA Sample"}}, "4:10": {"Count:3": 8, "Damage:2": 16, "OreDict:8": "", "id:8": "gregtech:gt.metaitem.98"}}, "taskID:8": "bq_standard:retrieval"}, "1:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 1, "index:3": 1, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 4, "OreDict:8": "", "id:8": "bartworks:BioLabModules"}}, "taskID:8": "bq_standard:retrieval"}, "2:10": {"autoConsume:1": 0, "consume:1": 0, "groupDetect:1": 0, "ignoreNBT:1": 0, "index:3": 2, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 0, "OreDict:8": "", "id:8": "bartworks:BioLabParts", "tag:10": {"Breedable:1": 1, "Color:11": [149, 132, 75], "DNA:10": {"Chance:3": 10000, "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Rarity:1": 1, "Tier:3": 0}, "Fluid:8": "escherichiakolifluid", "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Plasmid:10": {"Chance:3": 10000, "Name:8": "<PERSON>scher<PERSON><PERSON> koli", "Rarity:1": 1, "Tier:3": 0}, "Rarety:1": 1}}}, "taskID:8": "bq_standard:retrieval"}}}