{"preRequisites:9": {"0:10": {"questIDHigh:4": 0, "questIDLow:4": 84, "type:1": 1}, "1:10": {"questIDHigh:4": 0, "questIDLow:4": 59}, "2:10": {"questIDHigh:4": 0, "questIDLow:4": 76, "type:1": 1}}, "properties:10": {"betterquesting:10": {"autoClaim:1": 0, "desc:8": "Now that you have Steel, you can craft a sturdy enough Boiler to handle Lava. Steam is only outputted in the back, and you require Water input on the side and Lava input on the top. But as an added benefit, this Boiler will reward you with Obsidian!\n\n§3Did you know? Over 3 billion machines run on Lava!", "globalShare:1": 1, "icon:10": {"Count:3": 1, "Damage:2": 102, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}, "isGlobal:1": 0, "isMain:1": 0, "isSilent:1": 0, "lockedProgress:1": 0, "name:8": "§3§lAnother Use for Java, I Mean <PERSON>", "partySingleReward:1": 0, "questLogic:8": "AND", "repeatTime:3": -1, "repeat_relative:1": 1, "simultaneous:1": 0, "snd_complete:8": "random.levelup", "snd_update:8": "random.levelup", "taskLogic:8": "AND", "visibility:8": "NORMAL"}}, "questIDHigh:4": 0, "questIDLow:4": 61, "rewards:9": {"0:10": {"ignoreDisabled:1": 0, "index:3": 0, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 2, "Damage:2": 0, "OreDict:8": "", "id:8": "harvestcraft:cranberryjellysandwichItem"}}}, "1:10": {"ignoreDisabled:1": 0, "index:3": 1, "rewardID:8": "bq_standard:item", "rewards:9": {"0:10": {"Count:3": 10, "Damage:2": 0, "OreDict:8": "", "id:8": "dreamcraft:item.CoinTechnician"}}}}, "tasks:9": {"0:10": {"allowAnvil:1": 0, "allowCraft:1": 1, "allowCraftedFromStatistics:1": 0, "allowSmelt:1": 1, "ignoreNBT:1": 0, "index:3": 0, "partialMatch:1": 1, "requiredItems:9": {"0:10": {"Count:3": 1, "Damage:2": 102, "OreDict:8": "", "id:8": "gregtech:gt.blockmachines"}}, "taskID:8": "bq_standard:crafting"}}}